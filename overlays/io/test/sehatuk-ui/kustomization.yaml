apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: test

resources:
- ../../../../base/sehatuk-ui
- ../../../environment-configs/io-test
- sehatuk-cortex-ui-deployment.yaml
- sehatuk-cortex-ui-service.yaml
- sehatuk-cortex-ui-config-map.yaml

images:
- name: sehatuk-admin-ui
  newName: dxb.ocir.io/axpebais8u12/sehatuk-admin-ui
  newTag: 85bf97
- name: sehatuk-consumer-ui
  newName: dxb.ocir.io/axpebais8u12/sehatuk-consumer-ui
  newTag: 85bf97
- name: sehatuk-cortex-ui
  newName: dxb.ocir.io/axpebais8u12/sehatuk-cortex-ui
  newTag: 85bf97
- name: sehatuk-digital-twin-ui
  newName: dxb.ocir.io/axpebais8u12/sehatuk-digital-twin-ui
  newTag: 68a02392440d3ffa040f1c14c236bf3f887cbfea
- name: sehatuk-guided-care-ui
  newName: dxb.ocir.io/axpebais8u12/sehatuk-guided-care-ui
  newTag: 85bf97
- name: sehatuk-optima-ui
  newName: dxb.ocir.io/axpebais8u12/sehatuk-optima-ui
  newTag: 85bf97
- name: sehatuk-patient-profile-ui
  newName: dxb.ocir.io/axpebais8u12/sehatuk-patient-profile-ui
  newTag: 85bf97
- name: sehatuk-vendor-ui
  newName: dxb.ocir.io/axpebais8u12/sehatuk-vendor-ui
  newTag: 85bf97

patches:
- patch: |-
    - op: replace
      path: /metadata/name
      value: shared-vars-d1
  target:
    kind: ConfigMap
    name: shared-vars
    version: v1
