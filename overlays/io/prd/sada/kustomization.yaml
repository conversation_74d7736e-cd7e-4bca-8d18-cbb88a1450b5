apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: prd

resources:
- ../../../../base/sada
- ../../../environment-configs/io-prd

images:
  - name: sada-php-backend
    newName: dxb.ocir.io/axpebais8u12/sada-php-backend
    newTag: "096933"
  - name: sada-ws-backend
    newName: dxb.ocir.io/axpebais8u12/sada-ws-backend
    newTag: "096933"
  - name: sada-frontend
    newName: dxb.ocir.io/axpebais8u12/sada-frontend
    newTag: "ab8395"

patches:
  - target:
      kind: ConfigMap
      name: shared-vars
      version: v1
      group: ""
    patch: |-
      - op: replace
        path: /metadata/name
        value: shared-vars-d9

  - target:
      kind: Deployment
      name: sada-frontend
      version: v1
      group: apps
    patch: |-
      - op: replace
        path: "/spec/template/spec/containers/0/env/0"
        value:
          name: APP_NAME
          value: willis_prod