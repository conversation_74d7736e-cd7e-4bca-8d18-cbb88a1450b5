apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: prd

resources:
- ../../../../base/payment
- ../../../environment-configs/io-prd

components:
- ../../../../components/remove-hpa-and-resources

images:
- name: payment
  newName: dxb.ocir.io/axpebais8u12/payment
  newTag: sehatuk.release.0.0.160

# Hyper-Pay Gateway
configMapGenerator:
- behavior: merge
  literals:
  - hyper-pay.url="https://eu-test.oppwa.com/v1/"
  - hyper-pay.token="OGFjN2E0Yzc4YTkwOTc4MTAxOGE5Mjg4ZDNkZDAxYjh8cEFKVzRGdGRlSA=="
  - hyper-pay.entity-id="8ac7a4c78a909781018a928951a401bf"
  name: payment-configmap

patches:
- patch: |-
    - op: replace
      path: /metadata/name
      value: shared-vars-c8
  target:
    kind: ConfigMap
    name: shared-vars
    version: v1
