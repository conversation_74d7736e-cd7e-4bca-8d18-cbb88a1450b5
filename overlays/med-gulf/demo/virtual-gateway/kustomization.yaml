apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: demo

resources:
- ../../../../base/virtual-gateway
- ../../../environment-configs/med-gulf-demo

components:
- ../../../../components/remove-hpa-and-resources

images:
- name: virtual-gateway
  newName: dxb.ocir.io/axpebais8u12/virtual-gateway
  newTag: sehatuk.release.0.50

configMapGenerator:
- behavior: merge
  literals:
  - CONFERENCE_PROVIDER_TYPE=ANT_MEDIA
  - ANTMEDIA_URL=https://am-origin.dev.iohealth.com
  - ANTMEDIA_APPLICATIONNAME=VirtualGateway
  - ANTMEDIA_SDKSECRET=69ghnuVUrV63VZfQvvWIBkEOdevJ7Oep
  - TURN_SERVER_URL=turn:84.8.125.218:3478
  - TURN_SERVER_USERNAME=admin
  - TURN_SERVER_CREDENTIAL=admin
  name: virtual-gateway-configmap

patches:
- patch: |-
    - op: replace
      path: /metadata/name
      value: shared-vars-e4
  target:
    kind: ConfigMap
    name: shared-vars
    version: v1
