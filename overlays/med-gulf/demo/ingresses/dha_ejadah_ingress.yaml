apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-dha-ejadah-route
  annotations:
    nginx.ingress.kubernetes.io/proxy-buffer-size: "50m"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/use-forwarded-headers: "true"
    nginx.ingress.kubernetes.io/server-snippet: |
      set_real_ip_from 10.0.0.0/16;
      if ($http_x_forwarded_proto = http) {
        return 301 https://$server_name$request_uri;
      }

spec:
  rules:
    - host: "edd-mg.$(ROOT_DOMAIN)"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: sehatuk-dha-ejadah-ui
                port:
                  number: 3000