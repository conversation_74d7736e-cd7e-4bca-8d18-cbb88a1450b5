apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: demo

resources:
- ../../../../base/notification
- ../../../environment-configs/med-gulf-demo

components:
- ../../../../components/remove-hpa-and-resources

images:
- name: notification
  newName: dxb.ocir.io/axpebais8u12/notification
  newTag: sehatuk.release.0.0.55

configMapGenerator:
  - behavior: merge
    literals:
      - APPLICATION_SMS_GATEWAY_PROVIDER="DEEWAN"
      - APPLICATION_SMS_GATEWAY_APP_SID="89498856-779c-4de5-8ab2-239b3f555a7b"
      - APPLICATION_SMS_GATEWAY_SENDER_ID="iohealth"
      - APPLICATION_SMS_GATEWAY_SENDER_NAME="MEDGULF"
    name: notification-configmap

patches:
- patch: |-
    - op: replace
      path: /metadata/name
      value: shared-vars-c4
  target:
    kind: ConfigMap
    name: shared-vars
    version: v1
