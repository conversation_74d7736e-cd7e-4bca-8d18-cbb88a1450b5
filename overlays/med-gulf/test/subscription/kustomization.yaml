apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: test

resources:
- ../../../../base/subscription
- ../../../environment-configs/med-gulf-test

components:
- ../../../../components/remove-hpa-and-resources

images:
- name: subscription
  newName: dxb.ocir.io/axpebais8u12/subscription
  newTag: sehatuk.release.0.0.100

configMapGenerator:
- behavior: merge
  files:
  - firebase-secret-key.json
  name: subscription-configmap

patches:
- patch: |-
    - op: replace
      path: /metadata/name
      value: shared-vars-d2
  target:
    kind: ConfigMap
    name: shared-vars
    version: v1
