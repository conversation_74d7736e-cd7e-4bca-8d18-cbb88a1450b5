apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: test

resources:
- ../../../../base/knowledge-hub
- ../../../environment-configs/med-gulf-test

components:
- ../../../../components/remove-hpa-and-resources

images:
- name: knowledge-hub
  newName: dxb.ocir.io/axpebais8u12/knowledge-hub
  newTag: sehatuk.release.0.0.59

patches:
- patch: |-
    - op: replace
      path: /metadata/name
      value: shared-vars-c0
  target:
    kind: ConfigMap
    name: shared-vars
    version: v1

configMapGenerator:
  - behavior: merge
    name: knowledge-hub-configmap
    literals:
      - SPRING_DATASOURCE_URL=*********************************************************************************
