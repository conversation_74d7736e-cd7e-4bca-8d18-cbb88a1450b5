apiVersion: v1
kind: Service
metadata:
  name: metabase-svc
spec:
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80

---
apiVersion: v1
kind: Endpoints
metadata:
  name: metabase-svc
subsets:
  - addresses:
      - ip: ***********
    ports:
      - port: 80

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-reporting-route
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: "20m"
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/use-forwarded-headers: "true"
    nginx.ingress.kubernetes.io/server-snippet: |
      set_real_ip_from 10.0.0.0/16;
      if ($http_x_forwarded_proto = http) {
        return 301 https://$server_name$request_uri;
      }

spec:
  rules:
    - host: reporting.dev.iohealth.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: metabase-svc
                port:
                  number: 80
