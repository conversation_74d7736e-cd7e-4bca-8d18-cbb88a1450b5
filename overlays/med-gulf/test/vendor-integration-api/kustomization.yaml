apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: test

resources:
- ../../../../base/vendor-integration-api
- ../../../environment-configs/med-gulf-test

components:
- ../../../../components/remove-hpa-and-resources

images:
- name: vendor-integration-api
  newName: dxb.ocir.io/axpebais8u12/vendor-integration-api
  newTag: sehatuk.release.0.0.110

configMapGenerator:
- behavior: merge
  literals:
  - APOLLO_GRAPHQL_CLIENT_TOKEN_INTEGRATION_CLIENT_SECRET="HvWx9cujFvA6lSui3Vv3yCi4K62LD6q5"
  - SPRING_KAFKA_BOOTSTRAP_SERVERS=kafka-cluster-kafka-bootstrap:9092
  - SONAR_VENDOR_ID=14
  - SONAR_DEFAULT_BRANCH=19
  - SONAR_LICENSES=13579246810
  - TZ=Asia/Riyadh
  name: vendor-integration-api-configmap

patches:
- patch: |-
    - op: replace
      path: /metadata/name
      value: shared-vars-d7
  target:
    kind: ConfigMap
    name: shared-vars
    version: v1
