---
# Source: antmedia/templates/ams-k8s-mongodb.yaml
kind: Service
apiVersion: v1
metadata:
  name: mongo
spec:
  selector:
    app: mongo
  ports:
  - protocol: TCP
    port: 27017
---
# Source: antmedia/templates/ams-k8s-mongodb.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mongo
spec:
  selector:
    matchLabels:
      app: mongo
  replicas: 1
  template:
    metadata:
      labels:
        app: mongo
    spec:
      containers:
      - name: mongodb
        imagePullPolicy: Always
        image: mongo:6.0
        ports:
        - containerPort: 27017
        resources:
          limits:
            cpu: "2"
          requests:
            cpu: 500m
