---
# Source: antmedia/templates/tests/test-connection.yaml
apiVersion: v1
kind: Pod
metadata:
  name: "antmedia-test-connection"
  labels:
    helm.sh/chart: antmedia-2.12.0
    app.kubernetes.io/name: antmedia
    app.kubernetes.io/instance: antmedia
    app.kubernetes.io/version: "2.12.0"
    app.kubernetes.io/managed-by: He<PERSON>
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['antmedia:80']
  restartPolicy: Never
