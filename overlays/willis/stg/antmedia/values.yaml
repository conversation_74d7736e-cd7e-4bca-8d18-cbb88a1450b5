# Default values for antmedia-helm.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

licenseKey: ""

hostNetwork: true

kafkaURL: ""

UseGlobalIP: true
UseServerName: true
ReplaceCandidateAddress: true

origin: "am-origin.willis.stg.iohealth.com"
edge: "am-edge.willis.stg.iohealth.com"

#ingress:
#  enabled: true
#  pathType: ImplementationSpecific
#  apiVersion: ""
#  ingressClassName: ""
#  hostname: antmedia.local
#  path: /
#  annotations: {}
#  tls:
#    - secretName: ant-media-server-origin
#      hosts: []
#      autoGenerated: true
#      enabled: true
#    - secretName: ant-media-server-edge
#      hosts: []
#      autoGenerated: true
#      enabled: true
#  selfSigned: true
#
#  extraHosts: []
#  extraPaths: []
#  extraTls: []
#  secrets: []

#ingress:
#  enabled: true
#  className: "nginx"
#  annotations: {}
#    # kubernetes.io/ingress.class: nginx
#  # kubernetes.io/tls-acme: "true"
#  hosts:
#    - host: am.willis.stg.iohealth.com
#      paths:
#        - path: /
#          pathType: ImplementationSpecific
#  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

#image:
#  repository: nginx
#  pullPolicy: IfNotPresent
#  # Overrides the image tag whose default is the chart appVersion.
#  tag: 2.12.0
#
#imagePullSecrets: []
#nameOverride: ""
#fullnameOverride: ""

#serviceAccount:
#  # Specifies whether a service account should be created
#  create: true
#  # Annotations to add to the service account
#  annotations: {}
#  # The name of the service account to use.
#  # If not set and create is true, a name is generated using the fullname template
#  name: ""

#podAnnotations: {}
#
#podSecurityContext: {}
#  # fsGroup: 2000
#
#securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

#service:
#  type: ClusterIP
#  port: 80

#resources:
#  # We usually recommend not to specify default resources and to leave this as a conscious
#  # choice for the user. This also increases chances charts run on environments with little
#  # resources, such as Minikube. If you do want to specify resources, uncomment the following
#  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
#  # limits:
#  #   cpu: 100m
#  #   memory: 128Mi
#  requests:
#    cpu: 4000m
#  #   memory: 128Mi

#autoscalingEdge:
#  enabled: true
#  minReplicas: 1
#  maxReplicas: 10
#  targetCPUUtilizationPercentage: 60
#  # targetMemoryUtilizationPercentage: 80

#autoscalingOrigin:
#  enabled: true
#  minReplicas: 1
#  maxReplicas: 10
#  targetCPUUtilizationPercentage: 60
#  # targetMemoryUtilizationPercentage: 80

#OriginNodeSelector: {}
#EdgeNodeSelector: {}
#MongoDBNodeSelector: {}
#IngressNodeSelector: {}

#tolerations: []
#
#affinity: {}
#

#mongoDB: mongo


#image:
#  repository: antmedia/enterprise
#  tag: 2.12.0
#  pullPolicy: IfNotPresent
#
#provider:
#  aws: false
#  default: true

#aws.ssl.arn: {}

#annotations:
#  default: {
#    nginx.ingress.kubernetes.io/affinity: "cookie",
#    nginx.ingress.kubernetes.io/proxy-body-size: "0m",
#    nginx.ingress.kubernetes.io/session-cookie-name: "route",
#    nginx.ingress.kubernetes.io/session-cookie-expires: "172800",
#    nginx.ingress.kubernetes.io/session-cookie-max-age: "172800",
#    nginx.ingress.kubernetes.io/force-ssl-redirect: "true",
#    nginx.ingress.kubernetes.io/healthcheck-interval: "5s",
#    nginx.ingress.kubernetes.io/healthcheck-path: /,
#    nginx.ingress.kubernetes.io/healthcheck-port: "5080",
#    nginx.ingress.kubernetes.io/healthcheck-timeout: "3s",
#    nginx.ingress.kubernetes.io/proxy-next-upstream: error timeout http_502,
#    nginx.ingress.kubernetes.io/proxy-next-upstream-tries: "3",
#    nginx.ingress.kubernetes.io/ssl-protocols: TLSv1.2 TLSv1.3
#  }
#  aws: {
#    alb.ingress.kubernetes.io/scheme: internet-facing,
#    alb.ingress.kubernetes.io/target-type: ip,
#    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]',
#    alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig": { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}',
#    alb.ingress.kubernetes.io/target-group-attributes: stickiness.enabled=true
#  }



#affinityNode:
#  affinity:
#    nodeAffinity:
#      requiredDuringSchedulingIgnoredDuringExecution:
#        nodeSelectorTerms:
#        - matchExpressions:
#          - key: nodepool
#            operator: In
#
#OriginCpu: {}
#EdgeCpu: {}


#TurnStunServerURL:
#TurnUsername:
#TurnPassword:

#persistence:
#  config:
#    enabled: true
#    annotations:
#    "helm.sh/resource-policy": keep
#    ## bazarr configuration data Persistent Volume Storage Class
#    ## If defined, storageClassName: <storageClass>
#    ## If set to "-", storageClassName: "", which disables dynamic provisioning
#    ## If undefined (the default) or set to null, no storageClassName spec is
#    ##   set, choosing the default provisioner.  (gp2 on AWS, standard on
#    ##   GKE, AWS & OpenStack)
#    ##
#    # storageClass: "-"
#    ##
#    ## If you want to reuse an existing claim, you can pass the name of the PVC using
#    ## the existingClaim variable
#    # existingClaim: your-claim
#    accessMode: ReadWriteOnce
#    size: 1Gi
#    ## Do not delete the pvc upon helm uninstall
#    skipuninstall: true

