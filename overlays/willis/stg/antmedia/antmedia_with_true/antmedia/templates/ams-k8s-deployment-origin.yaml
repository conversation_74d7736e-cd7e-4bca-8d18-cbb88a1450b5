---
# Source: antmedia/templates/ams-k8s-deployment-origin.yaml
kind: Service
apiVersion: v1
metadata:
  name: ant-media-server-origin
spec:
  selector:
    app: ant-media-origin
  ports:
    - name: http
      protocol: TCP
      port: 5080
---
# Source: antmedia/templates/ams-k8s-deployment-origin.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ant-media-server-origin
spec:
  selector:
    matchLabels:
      app: ant-media-origin
  replicas: 1
  template:
    metadata:
      labels:
        app: ant-media-origin
    spec:
      
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app
                operator: In
                values:
                - ant-media-origin
                - ant-media-edge
            topologyKey: "kubernetes.io/hostname"
      hostNetwork: true
      
      dnsPolicy: ClusterFirstWithHostNet
      containers:
      - name: ant-media-server
        image: "antmedia/enterprise:2.12.0" 
        imagePullPolicy: IfNotPresent   
        args: ["-g", "true", "-s", "true", "-r", "true", "-m", "cluster", "-h", "mongo", "-l", "", "-a", "", "-n", "", "-w", "", "-k", ""]
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /
            port: 5080
            scheme: HTTP
          initialDelaySeconds: 15
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /
            port: 5080
            scheme: HTTP
          initialDelaySeconds: 5
          periodSeconds: 5
          successThreshold: 1
          timeoutSeconds: 1
        volumeMounts:
        - mountPath: /tmp
          name: temp-volume
      volumes:
      - hostPath:
          path: /temp-data
          type: DirectoryOrCreate
        name: temp-volume
