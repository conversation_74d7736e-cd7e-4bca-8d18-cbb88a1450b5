---
# Source: antmedia/charts/ingress-nginx/templates/clusterrolebinding.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    helm.sh/chart: ingress-nginx-4.4.0
    app.kubernetes.io/name: ingress-nginx
    app.kubernetes.io/instance: antmedia
    app.kubernetes.io/version: "1.5.1"
    app.kubernetes.io/part-of: ingress-nginx
    app.kubernetes.io/managed-by: Helm
  name: antmedia-ingress-nginx
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: antmedia-ingress-nginx
subjects:
  - kind: ServiceAccount
    name: antmedia-ingress-nginx
    namespace: "default"
