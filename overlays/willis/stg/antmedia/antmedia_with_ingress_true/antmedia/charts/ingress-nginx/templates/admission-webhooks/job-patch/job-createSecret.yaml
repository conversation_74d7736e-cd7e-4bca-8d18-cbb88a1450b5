---
# Source: antmedia/charts/ingress-nginx/templates/admission-webhooks/job-patch/job-createSecret.yaml
apiVersion: batch/v1
kind: Job
metadata:
  name: antmedia-ingress-nginx-admission-create
  namespace: default
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  labels:
    helm.sh/chart: ingress-nginx-4.4.0
    app.kubernetes.io/name: ingress-nginx
    app.kubernetes.io/instance: antmedia
    app.kubernetes.io/version: "1.5.1"
    app.kubernetes.io/part-of: ingress-nginx
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: admission-webhook
spec:
  template:
    metadata:
      name: antmedia-ingress-nginx-admission-create
      labels:
        helm.sh/chart: ingress-nginx-4.4.0
        app.kubernetes.io/name: ingress-nginx
        app.kubernetes.io/instance: antmedia
        app.kubernetes.io/version: "1.5.1"
        app.kubernetes.io/part-of: ingress-nginx
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/component: admission-webhook
    spec:
      containers:
        - name: create
          image: "registry.k8s.io/ingress-nginx/kube-webhook-certgen:v20220916-gd32f8c343@sha256:39c5b2e3310dc4264d638ad28d9d1d96c4cbb2b2dcfb52368fe4e3c63f61e10f"
          imagePullPolicy: IfNotPresent
          args:
            - create
            - --host=antmedia-ingress-nginx-controller-admission,antmedia-ingress-nginx-controller-admission.$(POD_NAMESPACE).svc
            - --namespace=$(POD_NAMESPACE)
            - --secret-name=antmedia-ingress-nginx-admission
          env:
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          securityContext: 
            allowPrivilegeEscalation: false
      restartPolicy: OnFailure
      serviceAccountName: antmedia-ingress-nginx-admission
      nodeSelector: 
        kubernetes.io/os: linux
      securityContext:
        fsGroup: 2000
        runAsNonRoot: true
        runAsUser: 2000
