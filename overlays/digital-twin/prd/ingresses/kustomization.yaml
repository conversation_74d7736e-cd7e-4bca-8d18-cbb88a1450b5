apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: prd

resources:
- ../../../../base/ingresses
- ../../../environment-configs/digital-twin-prd

patches:
  - target:
      kind: ConfigMap
      name: shared-vars
      version: v1
      group: ""
    patch: |-
      - op: replace
        path: /metadata/name
        value: shared-vars-b2

  - target:
      kind: Ingress
      name: ingress-digital-twin-route
      version: v1
      group: "networking.k8s.io"
    patch: |-
      - op: replace
        path: /spec/rules/0/host
        value: "dt.$(ROOT_DOMAIN)"