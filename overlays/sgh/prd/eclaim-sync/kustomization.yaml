apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: prd

resources:
- ../../../../base/eclaim-sync
- ../../../environment-configs/sgh-prd


images:
- name: eclaim-sync
  newName: dxb.ocir.io/axpebais8u12/eclaim-sync
  newTag: sehatuk.release.0.0.38

configMapGenerator:
  - behavior: merge
    literals:
      - SPRING_DATASOURCE_URL="*********************************************"
    name: eclaim-sync-configmap

patches:
- patch: |-
    - op: replace
      path: /metadata/name
      value: shared-vars-a5
  target:
    kind: ConfigMap
    name: shared-vars
    version: v1

- patch: |-
    - op: replace
      path: /spec/replicas
      value: 8
  target:
    kind: Deployment
