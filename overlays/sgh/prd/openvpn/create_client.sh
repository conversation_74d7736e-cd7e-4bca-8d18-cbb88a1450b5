#!/bin/bash

# make sure you are in the cluster context

if [ $# -ne 2 ]
then
  echo "Usage: $0 <CLIENT_KEY_NAME> <NAMESPACE>"
  exit
fi

KEY_NAME=$1
echo "KEY_NAME: $KEY_NAME"
NAMESPACE=$2
echo "NAMESPACE: $NAMESPACE"
POD_NAME=$(kubectl get pods -n "$NAMESPACE" -l "app=openvpn,release=openvpn" -o jsonpath='{.items[0].metadata.name}')
echo "POD_NAME: $POD_NAME"
SERVICE_NAME=$(kubectl get svc -n "$NAMESPACE" -l "app=openvpn,release=openvpn" -o jsonpath='{.items[0].metadata.name}')
echo "SERVICE_NAME: $SERVICE_NAME"

kubectl -n "$NAMESPACE" exec -it "$POD_NAME" -- /etc/openvpn/setup/newClientCert.sh "$KEY_NAME" "vp-in.sgh.iohealth.com"
kubectl -n "$NAMESPACE" exec -it "$POD_NAME" -- cat "/etc/openvpn/certs/pki/$KEY_NAME.ovpn" > "$KEY_NAME.ovpn"