apiVersion: apps/v1
kind: Deployment
metadata:
  name: chi
spec:
  replicas: 1
  selector:
    matchLabels:
      app: chi
  template:
    metadata:
      annotations:
        reloader.stakater.com/auto: "true"
      labels:
        app: chi
    spec:
      containers:
        - image: chi
          name: chi
          resources:
            requests:
              memory: "20Gi"
              cpu: "1"
          readinessProbe:
            httpGet:
              path: health
              port: 5003
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          livenessProbe:
            httpGet:
              path: health
              port: 5003
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          envFrom:
            - configMapRef:
                name: chi-configmap
          ports:
            - containerPort: 5003
              name: chi
      imagePullSecrets:
        - name: ocirsecret