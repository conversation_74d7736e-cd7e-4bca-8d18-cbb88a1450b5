# Environment Type
ENV=prd

# Root Environment Domain
ROOT_DOMAIN=stg.iohealth.com

# Keycloak
KEYCLOAK_REALM=stg-sehhati
KEYCLOAK_GATEWAY_WEB_APP_CLIENT_SECRET=WFRxTjl5RzFQMjFxcFU3VTRHV1NDZkhsTGZZQ0VUTnk=
KEYCLOAK_INTEGRATION_CLIENT_SECRET=cUw5YkZWbTVwNXp2VnR4M3lLN2FpZkJSY3NGUFVYSzc=

# Kafka
KAFKA_CLUSTER_NAMESPACE=stg

# Database Connection
DB_HOST=**********
DB_USER=cHJkX2RiX3VzZXI=
DB_PASS=YXNkM2hKZDNwS0pGWV82NzRxOV8=

# OCI
OCI_S3_REGION=me-dubai-1
OCI_S3_ENDPOINT=axpebais8u12.compat.objectstorage.me-dubai-1.oraclecloud.com
OCI_S3_ACCESS_KEY=NWNlNDc5YTE1ZDY1Zjg0NjVkNGRmOWU5MWMwYWNiMDJhYWVlM2JlYw==
OCI_S3_SECRET_KEY=N2FJbkZWMWtLRHhMRlhVL3FjSjdORGtycElTRmJFUE5wRkpMdEhlZFhhYz0=

# OpenSearch
OPEN_SEARCH_HOST_NAME=api.stg.iohealth.com
OPEN_SEARCH_USERNAME=admin
OPEN_SEARCH_PORT=443
OPEN_SEARCH_PASSWORD=YWRtaW5fQWRtaW5fMTIz
OPEN_SEARCH_PASSWORD_HASHED=$2y$12$.o0W71LPhNUWqv5/P8bVFeVew5bOAPcQ8pMexcn7NCy12xpiyUqpi
OPEN_SEARCH_HOST_SCHEMA=https

# Ingress Nginx
SSL_CERT=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
SSL_KEY=****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

# AI
AI_DB_URL=****************************************************************************************

# User Management
USER_MGMT_DB_URL=bXlzcWxnaXM6Ly9zdGdfZGJfdXNlcjp4eDNoSmQzcEJUUzhyOXEzTjRxOV9AMTAuMC4xMC4xNTcvc3RnX3VzZXJfbWFuYWdlbWVudAo=
PROJECT_NAME=IOHealth-Staging
*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

# File Service
FILE_SERVICE_S3_BUCKET=files.io-stg

# Flowise service
FLOWISE_URL=https://smart11.iohealth.im
FLOWISE_USERNAME=<EMAIL>
FLOWISE_PASSWORD=Pbiej5SOeuPQlM

# Gateway
NODE_ENV=staging
KEYCLOAK_RSA_PUBLIC_KEY=-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAv9K1w1RqSIohJUw3sJyp\nfsbnaochOE1I/ATEe1AZC4H9PeX0Afh1K7snZl2XHC5ptSFuwMvy+/UZaR/Fa2Fy\ndmXlpUtR3Si7yOdUvwf4HKDuiS+ns+oyXzmJqqDpaAqos9BLYvP/MMqLx7ZQEwv9\nSeDMrqTqDLBTW2T7IAZIRnU3oEF4hAWs1CCP9I5e8VhGFxdfjC9JUnV6jb3wwcYU\n4xwhQv9CSaZTOHOlV+kBs8CQUDaa5/AhG05yQVRw45C8vnqGnYE2NaW4tzQ/tCol\njRMXfAqhoBEpvxBL3IE9dtWgW501eko3i4rT0OwPaGxdMBH/4ZrmNv2cT82z/oQA\nZwIDAQAB\n-----END PUBLIC KEY-----

# HDS service
HDS_DB_ROOT_PASS=ZWJLKW5PWEg4MG87PHsqRjdEeiY=
HDS_METRIPORT_API_KEY=TWpFeE9tTmtZalkzT0dGaUxUQTNaVE10TkRKak5TMDVNMlkxTFRVMU5ERmpaakZtTVRWaE9BPT0=

# Heal Consumer & Health Program
HEAL_KAFKA_CONSUMER_GROUP_ID=sehatuk-stg-cluster
MEETORA_UI_BASE_DOMAIN=im.stg.iohealth.com
MEETORA_MODULES=00b509ae1948db6ea4e1e9449a30717197644b5a4cb8eed68a41353cea33114ecf7970d0761c9c1f12133223fe80311a2b8e0abb2b25c325e5b9a79b5281923a0c8f21e9d8a0257d4df83ef4a94450998138606e84da28f5374727367470cc9b53567e61c479398fb638548d6fcf002db7ba34d4afab18645a84a22be359da2705177ad68aa213c79d7dad4d424781b1752ff0b62ba6eb6471f92afc032c9645b7a8ff1229d0c90b1f785e2bcaa4ab4b48a6a1ea96aa5a34228a71ec41c161547a9c3ecda6e0f1dcf118e4720043167ea73977164dffd588f7dc2f86d8c2d7916040f1fcb3e75c10fa7604999ec9e893f77663bbbd7c8eecfd66ff6430639ffbd3
MEETORA_ENV=stg
MEETORA_BACKEND_BASE_DOMAIN=stg-api.iohealth.im
MEETORA_BACKEND_PORT=8085
MEETORA_BACKEND_UI_PORT=8090

# Optima Agent
OPTIMA_RCM_ENABLE=false

# Payer Integration API
ENABLE_IBAN_VERIFY_PROD=false

# Payment Service
PAYMENT_ENVIRONMENT_NAME=staging

# Sada
SADA_DB_URL=base64_encoded_url
SADA_S3_BUCKET=
SADA_ANTMEDIA_APPLICATION=

# Sehatuk UI
SADA_BASE_URL=https://sada.stg.iohealth.com/
SADA_API_BASE_URL=https://sada.stg.iohealth.com/api
SADA_API_ENV=stage

# Temporal IO
TEMPORAL_DB_HOST=**********
TEMPORAL_DB_USER=prd_db_user

# Google Space URLS
HEAL_GOOGLE_SPACE_URL=https%3A%2F%2Fchat.googleapis.com%2Fv1%2Fspaces%2FAAAATMbDmgA%2Fmessages%3Fkey%3DAIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI%26token%3D4tPayUyzEzqje9yrpZW-21PioK0d5hmaerkFN-M8WYo
HEALTH_PROGRAM_GOOGLE_SPACE_URL=https%3A%2F%2Fchat.googleapis.com%2Fv1%2Fspaces%2FAAAAcj8OAGM%2Fmessages%3Fkey%3DAIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI%26token%3Du3NOlb-q3o9abiuw3-3I9K0qjVTIWH_7tSwvPhvUsqY
KNOWLEDGE_HUB_GOOGLE_SPACE_URL=https://chat.googleapis.com/v1/spaces/AAAASejq_Sk/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=UYJ4j-4x7qp5qH9dbiYAcHjoRbTMdcBDiGUfFMUfeY4
MARKETPLACE_GOOGLE_SPACE_URL=https://chat.googleapis.com/v1/spaces/AAAA9rmhKfI/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=M4n_VzpsDpWE4bsHgrTE3C4q7wxCipRg_4Z9-ZUkMmk
NOTIFICATION_GOOGLE_SPACE_URL=https%3A%2F%2Fchat.googleapis.com%2Fv1%2Fspaces%2FAAAAWohPl4g%2Fmessages%3Fkey%3DAIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI%26token%3DxMy9rKPKLJlALcCdLMXPYSFPTVVuti_8iB1cvXJItLY
PAYMENT_GOOGLE_SPACE_URL=https%3A%2F%2Fchat.googleapis.com%2Fv1%2Fspaces%2FAAAAWLt-ee4%2Fmessages%3Fkey%3DAIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI%26token%3DAJHVdNhRlVduIIZjc54Qh5IBHKHuruz-oaCLkxVf-54
PRESCRIPTION_GOOGLE_SPACE_URL=https://chat.googleapis.com/v1/spaces/AAAAjT1Kee0/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=WB7RP7xLJTiOgMbSlXp5AUJKIOTN2aVCI1QejOmv0yM
REFERRAL_GOOGLE_SPACE_URL=https%3A%2F%2Fchat.googleapis.com%2Fv1%2Fspaces%2FAAAAmnGvduI%2Fmessages%3Fkey%3DAIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI%26token%3DC7dctDamf6n-EeJm78n-pLYCCxfv8KGnbvPw4LFvx04
USER_MGMT_GOOGLE_SPACE_URL=https://chat.googleapis.com/v1/spaces/AAAAtl-NWVA/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=ty_mE-XVuMlGOmy714kFL1baEi6EUEXv7qzCIBdL4GU
ECLAIM_GOOGLE_SPACE_URL=https://chat.googleapis.com/v1/spaces/AAAA0TN2SOk/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=Nk9wFxXCi3v-2YYK231G3jMTxv7KOYoab-cx9v3_ajY
PROVIDER_INTEG_GOOGLE_SPACE_URL=https://chat.googleapis.com/v1/spaces/AAAA4QbUKhs/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=gqGNYUXgyXB0TdxNkM8VO9So767akBKfaz2sqqcms4U
OPTIMA_GOOGLE_SPACE_URL=https://chat.googleapis.com/v1/spaces/AAAAWa1qqY4/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=oHKgwHB-RBXKIfQsuLKG6SpSazVbbx1XZQ9Jwnrzfjc


##### Feature Flags #####

# Activity Tracker
FEATURE_FLAG_ACTIVITY_TRACKER_FEATURE_ENABLED=False

# Health Programs
FEATURE_FLAG_MULTIPLE_APPOINTMENT_SLOTS_FEATURE_ENABLED=False
FEATURE_FLAG_MEDICAL_DEVICE_FEATURE_ENABLED=True
FEATURE_FLAG_IMMEDIATE_CALL_FEATURE_ENABLED=False
FEATURE_FLAG_GUEST_APPOINTMENT_FEATURE_ENABLED=False
FEATURE_FLAG_HOME_CARE_SERVICES_FEATURE_ENABLED=False
GUIDED_CARE_TEAM_FILTER_ELIGIBLE_TEAM_BY_PATIENT_NETWORK=False

# Marketplace
FEATURE_FLAG_MARKETPLACE_FEATURE_ENABLED=True
FEATURE_FLAG_HEALTH_PACKAGE_FEATURE_ENABLED=True
FEATURE_FLAG_KNOWLEDGE_HUB_FEATURE_ENABLED=True
FEATURE_FLAG_IN_PERSON_CHECKIN_FEATURE_ENABLED=False
FEATURE_FLAG_SSO_FEATURE_ENABLED=True
FEATURE_FLAG_UAEPASS_FEATURE_ENABLED=False
FEATURE_FLAG_ADD_MEMBER_CARD_FEATURE_ENABLED=True
FEATURE_FLAG_PRODUCT_FEED_IGNORE_AVAILABILITY=True
FEATURE_FLAG_DIGITAL_TWIN_FEATURE_ENABLED=True
FEATURE_FLAG_MY_HEALTH_FEATURE_ENABLED=False

# User Management
FEATURE_FLAG_CUSTOMER_REGISTRATION_FEATURE_ENABLED=True
FEATURE_FLAG_DELETE_CUSTOMER_ACCOUNT_FEATURE_ENABLED=True
FEATURE_FLAG_ADD_NATIONAL_ID_FEATURE_ENABLED=True
FEATURE_FLAG_DEPENDENT_CREATION_FEATURE_ENABLED=True
FEATURE_FLAG_FILTER_DOCTORS_BY_PATIENT_NETWORK=False
FEATURE_FLAG_TWO_FACTOR_AUTHENTICATION_FEATURE_ENABLED=False

# Prescription
FEATURE_FLAG_PRESCRIPTION_FEATURE_ENABLED=False
FEATURE_FLAG_PRESCRIPTION_PICKUP_FEATURE_ENABLED=False
FEATURE_FLAG_MANUAL_PRESCRIPTION_FEATURE_ENABLED=True