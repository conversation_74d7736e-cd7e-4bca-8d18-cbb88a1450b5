# Environment Type
ENV=prd

# Root Environment Domain
ROOT_DOMAIN=demo.iohealth.com

# Keycloak
KEYCLOAK_REALM=prd-sehhati
KEYCLOAK_GATEWAY_WEB_APP_CLIENT_SECRET=WFRxTjl5RzFQMjFxcFU3VTRHV1NDZkhsTGZZQ0VUTnk=
KEYCLOAK_INTEGRATION_CLIENT_SECRET=cUw5YkZWbTVwNXp2VnR4M3lLN2FpZkJSY3NGUFVYSzc=

# Kafka
KAFKA_CLUSTER_NAMESPACE=demo

# Database Connection
DB_HOST=***********
DB_USER=cHJkX2RiX3VzZXI=
DB_PASS=eHgzaEpkM3BCVFM4cjlxM040cTlf

# OCI
OCI_S3_REGION=me-dubai-1
OCI_S3_ENDPOINT=axpebais8u12.compat.objectstorage.me-dubai-1.oraclecloud.com
OCI_S3_ACCESS_KEY=NWNlNDc5YTE1ZDY1Zjg0NjVkNGRmOWU5MWMwYWNiMDJhYWVlM2JlYw==
OCI_S3_SECRET_KEY=N2FJbkZWMWtLRHhMRlhVL3FjSjdORGtycElTRmJFUE5wRkpMdEhlZFhhYz0=

# OpenSearch
OPEN_SEARCH_HOST_NAME=api.demo.iohealth.com
OPEN_SEARCH_USERNAME=admin
OPEN_SEARCH_PORT=443
OPEN_SEARCH_PASSWORD=YWRtaW5fQWRtaW5fMTIz
OPEN_SEARCH_PASSWORD_HASHED=$2y$12$.o0W71LPhNUWqv5/P8bVFeVew5bOAPcQ8pMexcn7NCy12xpiyUqpi
OPEN_SEARCH_HOST_SCHEMA=https

# Ingress Nginx
SSL_CERT=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
SSL_KEY=****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

# AI
AI_DB_URL=bXlzcWxnaXM6Ly9kZW1vX2RiX3VzZXI6Zms4aktmNFtOWURdNnQwdzQ0VmE3TEAxMC4wLjEwLjcyL2RlbW9fYWkK

# User Management
USER_MGMT_DB_URL=bXlzcWxnaXM6Ly9kZW1vX2RiX3VzZXI6Zms4aktmNFtOWURdNnQwdzQ0VmE3TEAxMC4wLjEwLjcyL2RlbW9fdXNlcl9tYW5hZ2VtZW50
PROJECT_NAME=IOHealth-Demo
*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

# File Service
FILE_SERVICE_S3_BUCKET=files.io-demo

# Flowise service
FLOWISE_URL=https://fts35dfc7lknaf-8888.proxy.runpod.net
FLOWISE_USERNAME=<EMAIL>
FLOWISE_PASSWORD=Pbiej5SOeuPQlM

# Gateway
NODE_ENV=demo
KEYCLOAK_RSA_PUBLIC_KEY=-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAv9K1w1RqSIohJUw3sJyp\nfsbnaochOE1I/ATEe1AZC4H9PeX0Afh1K7snZl2XHC5ptSFuwMvy+/UZaR/Fa2Fy\ndmXlpUtR3Si7yOdUvwf4HKDuiS+ns+oyXzmJqqDpaAqos9BLYvP/MMqLx7ZQEwv9\nSeDMrqTqDLBTW2T7IAZIRnU3oEF4hAWs1CCP9I5e8VhGFxdfjC9JUnV6jb3wwcYU\n4xwhQv9CSaZTOHOlV+kBs8CQUDaa5/AhG05yQVRw45C8vnqGnYE2NaW4tzQ/tCol\njRMXfAqhoBEpvxBL3IE9dtWgW501eko3i4rT0OwPaGxdMBH/4ZrmNv2cT82z/oQA\nZwIDAQAB\n-----END PUBLIC KEY-----

# HDS service, No HDS for demo
HDS_DB_ROOT_PASS=
HDS_METRIPORT_API_KEY=

# Heal Consumer & Health Program
HEAL_KAFKA_CONSUMER_GROUP_ID=sehatuk-demo-cluster
MEETORA_UI_BASE_DOMAIN=im.demo.sehacity.com
MEETORA_MODULES=00c7f8ed613ec08d8ef23ab261c47c852281473bdf1e0c6c34eae83deb57686578c69659db5c8696e0654b6e0b2afc3f340ef292a5d746fb1ea30c0606a9937eff9c5bd226001e684edf5704175849166220fecebcb3d91f9f8af8135ce1acffff1d9660598edbd63e303c12ca952f3676528d5adef511d3b774eb6901d922bf18386a1d2bf5a10f2ca2fdd2ef27e32d32643de8826cbfb58f4b461c486993a638df77184b362eefd947173be3d4378e8b7f0bbc5c9259ac297373e4a75869a91ceb0a0778a4a728d74d409123401eb76542bff715b0844a87cba6ca930eeeba723e390dfbe2662314ffe21f8f25a06bf1f5a54537900185861255caa71a81e0d9
MEETORA_ENV=demo
MEETORA_BACKEND_BASE_DOMAIN=demo-api.sehacity.im
MEETORA_BACKEND_PORT=8085
MEETORA_BACKEND_UI_PORT=8090

# Optima Agent
OPTIMA_RCM_ENABLE=false

# Payer Integration API
ENABLE_IBAN_VERIFY_PROD=false

# Payment Service
PAYMENT_ENVIRONMENT_NAME=demo

# Sada
SADA_DB_URL=base64_encoded_url
SADA_S3_BUCKET=
SADA_ANTMEDIA_APPLICATION=

# Sehatuk UI
SADA_BASE_URL=https://sada.test.iohealth.com/
SADA_API_BASE_URL=https://sada.test.iohealth.com/api
SADA_API_ENV=demo

# Temporal IO
TEMPORAL_DB_HOST=***********
TEMPORAL_DB_USER=prd_db_user

# Google Space URLS
HEAL_GOOGLE_SPACE_URL=https://chat.googleapis.com/v1/spaces/AAAA4GPUqjQ/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=xbd93dY6qSfdBYLb81vlr2rnolExXDmaHQJPJ7YdLcQ
HEALTH_PROGRAM_GOOGLE_SPACE_URL=https://chat.googleapis.com/v1/spaces/AAAAAQ0SS_A/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=BLMXwZj1wRjKuEKuL4bdm82stgGkvuDZpEizTCrnNEc
KNOWLEDGE_HUB_GOOGLE_SPACE_URL=https://chat.googleapis.com/v1/spaces/AAAAALX-VLQ/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=-yXZiPvc_7JKthhyAeuFMRaH9iHc7M4FmbMOpzBqRc8
MARKETPLACE_GOOGLE_SPACE_URL=https://chat.googleapis.com/v1/spaces/AAAAxgb3xjI/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=AGr_RbK3YHMUL-Lul-2oZ9D2AAuHNkXwKEhQmBshUyw
NOTIFICATION_GOOGLE_SPACE_URL=https://chat.googleapis.com/v1/spaces/AAAAsGkL7Ig/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=CIbBIIqhhSih-3xSIEmbjXRKczLxYr93iMS10PQCirE
PAYMENT_GOOGLE_SPACE_URL=https://chat.googleapis.com/v1/spaces/AAAAshViOlE/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=vR8Vjs0t2MG8nJoJAyZbfCiOns19QS30ETSwOZfbQH0
PRESCRIPTION_GOOGLE_SPACE_URL=https://chat.googleapis.com/v1/spaces/AAAAh7uIQBI/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=EZyA6-xQxM4aAaDs4hcIwY9RSwKKXp2-R-e8d8lC0Sw
REFERRAL_GOOGLE_SPACE_URL=https://chat.googleapis.com/v1/spaces/AAAAkc2g8zE/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=4fBVYSmvK8bs9DS6EW6OWEuLRONNWIUzvoC1CvFzkGc
USER_MGMT_GOOGLE_SPACE_URL=https://chat.googleapis.com/v1/spaces/AAAAfC9Dvx4/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=hhjWu-kr0iDvj1SiVcNvBrj9fhON5NmAUzq4RFWq-cY
ECLAIM_GOOGLE_SPACE_URL=https://chat.googleapis.com/v1/spaces/AAAAP0SQE9Q/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=naMMhgukhCi_gD3tmmS22VDRPMkgsMkrizqpha4KLJk
PROVIDER_INTEG_GOOGLE_SPACE_URL=https://chat.googleapis.com/v1/spaces/AAAAcTmGoZc/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=TLf23gQUAG9GZ0d9LuKNh4BejgYV5uY__9sFE6ABNaI
OPTIMA_GOOGLE_SPACE_URL=https://chat.googleapis.com/v1/spaces/AAAASnCul6A/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=_J2CzxMK9bzpx2qtzTBXK0iztjUSDEou52pTKYge5NQ


##### Feature Flags #####

# Activity Tracker
FEATURE_FLAG_ACTIVITY_TRACKER_FEATURE_ENABLED=False

# Health Programs
FEATURE_FLAG_MULTIPLE_APPOINTMENT_SLOTS_FEATURE_ENABLED=False
FEATURE_FLAG_MEDICAL_DEVICE_FEATURE_ENABLED=False
FEATURE_FLAG_IMMEDIATE_CALL_FEATURE_ENABLED=False
FEATURE_FLAG_GUEST_APPOINTMENT_FEATURE_ENABLED=False
FEATURE_FLAG_HOME_CARE_SERVICES_FEATURE_ENABLED=False
GUIDED_CARE_TEAM_FILTER_ELIGIBLE_TEAM_BY_PATIENT_NETWORK=False

# Marketplace
FEATURE_FLAG_MARKETPLACE_FEATURE_ENABLED=True
FEATURE_FLAG_HEALTH_PACKAGE_FEATURE_ENABLED=True
FEATURE_FLAG_KNOWLEDGE_HUB_FEATURE_ENABLED=True
FEATURE_FLAG_IN_PERSON_CHECKIN_FEATURE_ENABLED=False
FEATURE_FLAG_SSO_FEATURE_ENABLED=True
FEATURE_FLAG_UAEPASS_FEATURE_ENABLED=False
FEATURE_FLAG_ADD_MEMBER_CARD_FEATURE_ENABLED=True
FEATURE_FLAG_PRODUCT_FEED_IGNORE_AVAILABILITY=True
FEATURE_FLAG_DIGITAL_TWIN_FEATURE_ENABLED=True
FEATURE_FLAG_MY_HEALTH_FEATURE_ENABLED=True

# User Management
FEATURE_FLAG_CUSTOMER_REGISTRATION_FEATURE_ENABLED=True
FEATURE_FLAG_DELETE_CUSTOMER_ACCOUNT_FEATURE_ENABLED=True
FEATURE_FLAG_ADD_NATIONAL_ID_FEATURE_ENABLED=True
FEATURE_FLAG_DEPENDENT_CREATION_FEATURE_ENABLED=True
FEATURE_FLAG_FILTER_DOCTORS_BY_PATIENT_NETWORK=False
FEATURE_FLAG_TWO_FACTOR_AUTHENTICATION_FEATURE_ENABLED=False

# Prescription
FEATURE_FLAG_PRESCRIPTION_FEATURE_ENABLED=True
FEATURE_FLAG_PRESCRIPTION_PICKUP_FEATURE_ENABLED=False
FEATURE_FLAG_MANUAL_PRESCRIPTION_FEATURE_ENABLED=True