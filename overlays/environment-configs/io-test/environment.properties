# Environment Type
ENV=test

# Root Environment Domain
ROOT_DOMAIN=test.iohealth.com

# Keycloak
KEYCLOAK_REALM=test-sehhati
KEYCLOAK_GATEWAY_WEB_APP_CLIENT_SECRET=WFRxTjl5RzFQMjFxcFU3VTRHV1NDZkhsTGZZQ0VUTnk=
KEYCLOAK_INTEGRATION_CLIENT_SECRET=cUw5YkZWbTVwNXp2VnR4M3lLN2FpZkJSY3NGUFVYSzc=

# Kafka
KAFKA_CLUSTER_NAMESPACE=test

# Database Connection
DB_HOST=*********
DB_USER=dGVzdF9kYl91c2Vy
DB_PASS=WjJvNWFYaE9lbnBuUlVBITE1

# OCI
OCI_S3_REGION=me-dubai-1
OCI_S3_ENDPOINT=axpebais8u12.compat.objectstorage.me-dubai-1.oraclecloud.com
OCI_S3_ACCESS_KEY=NWNlNDc5YTE1ZDY1Zjg0NjVkNGRmOWU5MWMwYWNiMDJhYWVlM2JlYw==
OCI_S3_SECRET_KEY=N2FJbkZWMWtLRHhMRlhVL3FjSjdORGtycElTRmJFUE5wRkpMdEhlZFhhYz0=

# OpenSearch
OPEN_SEARCH_HOST_NAME=api.test.iohealth.com
OPEN_SEARCH_USERNAME=admin
OPEN_SEARCH_PORT=443
OPEN_SEARCH_PASSWORD=YWRtaW5fQWRtaW5fMTIz
OPEN_SEARCH_PASSWORD_HASHED=$2y$12$.o0W71LPhNUWqv5/P8bVFeVew5bOAPcQ8pMexcn7NCy12xpiyUqpi
OPEN_SEARCH_HOST_SCHEMA=https

# Ingress Nginx
SSL_CERT=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
SSL_KEY=****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

# AI
AI_DB_URL=bXlzcWxnaXM6Ly90ZXN0X2RiX3VzZXI6WjJvNWFYaE9lbnBuUlVBITE1QDEwLjAuMTAuMy90ZXN0X2Fp

# User Management
USER_MGMT_DB_URL=bXlzcWxnaXM6Ly90ZXN0X2RiX3VzZXI6WjJvNWFYaE9lbnBuUlVBITE1QDEwLjAuMTAuMy90ZXN0X3VzZXJtYW5hZ21lbnQ=
PROJECT_NAME=IOHealth-Test
KEYCLOAK_RSA_PRIVATE_KEY=-----BEGIN RSA PRIVATE KEY-----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\n50nTOUm0tFHyS/3XduWLzKJQA/CHNgk=\n-----END RSA PRIVATE KEY-----

# File Service
FILE_SERVICE_S3_BUCKET=files.io-test

# Flowise service
FLOWISE_URL=https://fts35dfc7lknaf-8888.proxy.runpod.net
FLOWISE_USERNAME=<EMAIL>
FLOWISE_PASSWORD=Pbiej5SOeuPQlM

# Gateway
NODE_ENV=test
KEYCLOAK_RSA_PUBLIC_KEY=-----BEGIN PUBLIC KEY-----\nMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAwpJ+hR6DzekCvXne8Y+o\n/TLprzuMjMc+accIze+gX5ESH0tDYbI+HZKGS7A7+AGhoDXYoO221aj3bdezorDp\na0tM7cIfiG5np223Hdmg9edrvAGf2Q8Ja3GBxg08fxA5y9q2riGxJd7r7OcKafVX\nnS9vm+u9fDar4ZMZzJkFhwNE1igvF75Bit/fIstqjKUwPDcDJt4bzgqnWtEkomOv\nxqLgbVmiCbH0l3yAbSSQDEan2nuaeW6OFblLGunEbFbtSWx27AoUFMgoEwmaTr/e\nSQKNTRTKOTflnEwFMnByZYkt6J/OVD1lb+ZbxGUZHN5W57iz8EAzvDDFouqso/le\nbbuZdCshAIGI7s7w+svHyPQIaTU4Ug4W1moMhy+OJy+0uvvjW2OKMEuEIACiCMbG\nvk38CQXXDis1VdCyxACj1Lk5/pZJ7hEmTHoWQkls2Bb8OlkifFxRyT1RKVYYu/Y9\nkQ4HKwZySXp4nxHw7YQy9EPbCZN6Uz5bKEoqfCslStpTaTw3cs4ilbfCjEztvZRK\nhNXQ6WGJCIGQpYjJ3zIHlfUzg+DolUF0hMf2Kn085KOkXAOVr959+fuk/9XxDhJ8\nI+30GqKDM+B6kcgluZZNIac9iTtsO/VSdiMsTA9ZStq7c9lqNsiRkYfQEM3wKHRC\nCHlmuS3oi2Az2m4VgE4PDFkCAwEAAQ==\n-----END PUBLIC KEY-----

# HDS service, No HDS for test
HDS_DB_ROOT_PASS=
HDS_METRIPORT_API_KEY=

# Heal Consumer & Health Program
HEAL_KAFKA_CONSUMER_GROUP_ID=sehatuk2-test
MEETORA_UI_BASE_DOMAIN=im.test.sehacity.com
MEETORA_MODULES=00b509ae1948db6ea4e1e9449a30717197644b5a4cb8eed68a41353cea33114ecf7970d0761c9c1f12133223fe80311a2b8e0abb2b25c325e5b9a79b5281923a0c8f21e9d8a0257d4df83ef4a94450998138606e84da28f5374727367470cc9b53567e61c479398fb638548d6fcf002db7ba34d4afab18645a84a22be359da2705177ad68aa213c79d7dad4d424781b1752ff0b62ba6eb6471f92afc032c9645b7a8ff1229d0c90b1f785e2bcaa4ab4b48a6a1ea96aa5a34228a71ec41c161547a9c3ecda6e0f1dcf118e4720043167ea73977164dffd588f7dc2f86d8c2d7916040f1fcb3e75c10fa7604999ec9e893f77663bbbd7c8eecfd66ff6430639ffbd3
MEETORA_ENV=test
MEETORA_BACKEND_BASE_DOMAIN=test-api.sehacity.im
MEETORA_BACKEND_PORT=8085
MEETORA_BACKEND_UI_PORT=8090

# Optima Agent
OPTIMA_RCM_ENABLE=false

# Payer Integration API
ENABLE_IBAN_VERIFY_PROD=false

# Payment Service
PAYMENT_ENVIRONMENT_NAME=test

# Sada
SADA_DB_URL=********************************************************************************
SADA_S3_BUCKET=streaming_test
SADA_ANTMEDIA_APPLICATION=iohealthtest

# Sehatuk UI
SADA_BASE_URL=https://sada.test.iohealth.com/
SADA_API_BASE_URL=https://sada.test.iohealth.com/api
SADA_API_ENV=io.test

# Temporal IO
TEMPORAL_DB_HOST=*********
TEMPORAL_DB_USER=test_db_user

# Cortex
AGENT_N8N_API_KEY=ZXlKaGJHY2lPaUpJVXpJMU5pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SnpkV0lpT2lJeE56aGpNRGszWlMxa1pHRTVMVFEzTlRjdFlUWmhNUzFrWTJGaVpUSXpZelJqT0RRaUxDSnBjM01pT2lKdU9HNGlMQ0poZFdRaU9pSndkV0pzYVdNdFlYQnBJaXdpYVdGMElqb3hOelE0T0RrME9UY3hmUS5LcWFmYzliOEgtUUU4UjRuTmJEeHUydElGWlVQZHF1YTRFeWRJVHdRM08w

# Google Space URLS
HEAL_GOOGLE_SPACE_URL=https%3A%2F%2Fchat.googleapis.com%2Fv1%2Fspaces%2FAAAAbJXNZTQ%2Fmessages%3Fkey%3DAIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI%26token%3Daz-uyjgs1V0NE3JI-yzFHoZa2whEExi1z1rBlPtmyjs
HEALTH_PROGRAM_GOOGLE_SPACE_URL=https%3A%2F%2Fchat.googleapis.com%2Fv1%2Fspaces%2FAAAAgeqSlis%2Fmessages%3Fkey%3DAIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI%26token%3DA_lFI00juPTBwx_2A11LvmuTvNWqILxIZXarwzaAQtk
KNOWLEDGE_HUB_GOOGLE_SPACE_URL=https://chat.googleapis.com/v1/spaces/AAAAgWnqHiU/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=onSpjq0XhmlC8ZBli8L003gAty_36gF5zRRNNe1tbWI
MARKETPLACE_GOOGLE_SPACE_URL=https://chat.googleapis.com/v1/spaces/AAAACnC6Fd4/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=uzskz_Hz6hN-kqaPYxhptQXeZuKNvcWooXpyXLQJDro
NOTIFICATION_GOOGLE_SPACE_URL=https%3A%2F%2Fchat.googleapis.com%2Fv1%2Fspaces%2FAAAAuDRLYU4%2Fmessages%3Fkey%3DAIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI%26token%3DOLpT23a3T-hE5IR0DagZuGP-f9-rBUzbx0XfubKTSZg
PAYMENT_GOOGLE_SPACE_URL=https://chat.googleapis.com/v1/spaces/AAAAXy_vuqY/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=ki22ueSsbUMEPeuA3D2OOlYJ1hzA6Njy4kDSXxs2tZ8
PRESCRIPTION_GOOGLE_SPACE_URL=https://chat.googleapis.com/v1/spaces/AAAAuXWqUu4/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=_UrDgObEhCV9bOGxFiV4ESVzHg263-HvSMwioxWhjRk
REFERRAL_GOOGLE_SPACE_URL=https://chat.googleapis.com/v1/spaces/AAAA-TT_lnI/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=dvCvMSsfCbTTiuWBNOhJ8o6s-wLCqC8yJP1dfRiIQvw
USER_MGMT_GOOGLE_SPACE_URL=https://chat.googleapis.com/v1/spaces/AAAAm2V7cLc/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=2yRvTAmHSslyrt7rhNYfaZsAHjq08xpEvTBTmJhQaqo
ECLAIM_GOOGLE_SPACE_URL=https://chat.googleapis.com/v1/spaces/AAAATkDF0vU/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=SueUU-hUEd37E6dB1FfDYsNjvcs0ZnAO_VDvCNNpkPY
PROVIDER_INTEG_GOOGLE_SPACE_URL=https://chat.googleapis.com/v1/spaces/AAAAxcpwotE/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=Q3wpN1I2cvtOASItFkYXDgwFwpEU_wU4JMP-V0UOjO0
OPTIMA_GOOGLE_SPACE_URL=https://chat.googleapis.com/v1/spaces/AAAAZgpTJLU/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=iBCN3EA68mKaA-gW5TJxFLnZegqUKD1BWDBh7A8_4uc


##### Feature Flags #####

# Activity Tracker
FEATURE_FLAG_ACTIVITY_TRACKER_FEATURE_ENABLED=False

# Health Programs
FEATURE_FLAG_MULTIPLE_APPOINTMENT_SLOTS_FEATURE_ENABLED=True
FEATURE_FLAG_MEDICAL_DEVICE_FEATURE_ENABLED=True
FEATURE_FLAG_IMMEDIATE_CALL_FEATURE_ENABLED=True
FEATURE_FLAG_GUEST_APPOINTMENT_FEATURE_ENABLED=False
FEATURE_FLAG_HOME_CARE_SERVICES_FEATURE_ENABLED=False
GUIDED_CARE_TEAM_FILTER_ELIGIBLE_TEAM_BY_PATIENT_NETWORK=False
FEATURE_FLAG_PROGRAM_PROFILE_FEATURE_ENABLED=False
FEATURE_FLAG_REFERRAL_FEATURE_ENABLED=False

# Marketplace
FEATURE_FLAG_MARKETPLACE_FEATURE_ENABLED=True
FEATURE_FLAG_HEALTH_PACKAGE_FEATURE_ENABLED=True
FEATURE_FLAG_KNOWLEDGE_HUB_FEATURE_ENABLED=True
FEATURE_FLAG_IN_PERSON_CHECKIN_FEATURE_ENABLED=False
FEATURE_FLAG_SSO_FEATURE_ENABLED=True
FEATURE_FLAG_UAEPASS_FEATURE_ENABLED=False
FEATURE_FLAG_ADD_MEMBER_CARD_FEATURE_ENABLED=True
FEATURE_FLAG_PRODUCT_FEED_IGNORE_AVAILABILITY=True
FEATURE_FLAG_DIGITAL_TWIN_FEATURE_ENABLED=True
FEATURE_FLAG_MY_HEALTH_FEATURE_ENABLED=True

# User Management
FEATURE_FLAG_CUSTOMER_REGISTRATION_FEATURE_ENABLED=True
FEATURE_FLAG_DELETE_CUSTOMER_ACCOUNT_FEATURE_ENABLED=False
FEATURE_FLAG_ADD_NATIONAL_ID_FEATURE_ENABLED=True
FEATURE_FLAG_DEPENDENT_CREATION_FEATURE_ENABLED=True
FEATURE_FLAG_FILTER_DOCTORS_BY_PATIENT_NETWORK=False
FEATURE_FLAG_TWO_FACTOR_AUTHENTICATION_FEATURE_ENABLED=True

# Prescription
FEATURE_FLAG_PRESCRIPTION_FEATURE_ENABLED=True
FEATURE_FLAG_PRESCRIPTION_PICKUP_FEATURE_ENABLED=False
FEATURE_FLAG_MANUAL_PRESCRIPTION_FEATURE_ENABLED=False

# Payer Integration API
FEATURE_FLAG_COMPLAINT_FEATURE_ENABLED=True
FEATURE_FLAG_FILE_MANAGEMENT_ENABLED=False
FEATURE_FLAG_PAYERS_ENABLED=False
FEATURE_FLAG_INSURANCE_APPROVAL_ENABLED=False
FEATURE_FLAG_CALLBACK_REQUEST_ENABLED=False
FEATURE_FLAG_ACTIVITY_TRACKER_ENABLED=True
