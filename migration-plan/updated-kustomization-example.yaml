# Example: overlays/io/test/gateway/kustomization.yaml
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

# Updated namespace with prefix for multi-tenancy
namespace: io-test

resources:
- ../../../../base/gateway
- ../../../environment-configs/io-test
- ../../../multi-tenant-security/io-test-namespace.yaml  # Add namespace creation

components:
  - ../../../../components/remove-hpa-and-resources

images:
  - name: gateway
    newName: dxb.ocir.io/axpebais8u12/apollo
    newTag: sehatuk.release.zipkin.0.4

configMapGenerator:
  - name: gateway-configmap
    behavior: merge
    literals:
      - URL_23=http://digital-twin.io-test:7030/graphql  # Updated service references
      - URL_24=http://vendor-integration-api.io-test:7020/graphql

patches:
  - target:
      kind: ConfigMap
      name: shared-vars
      version: v1
      group: ""
    patch: |-
      - op: replace
        path: /metadata/name
        value: shared-vars-io-test  # Environment-specific naming

# Add namespace prefix to all resources
namePrefix: io-test-

# Add common labels for tenant identification
commonLabels:
  tenant: io
  environment: test
  app.kubernetes.io/part-of: io-platform
