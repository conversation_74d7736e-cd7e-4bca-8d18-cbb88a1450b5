# Template for creating tenant namespaces with proper isolation
apiVersion: v1
kind: Namespace
metadata:
  name: io-${ENVIRONMENT}
  labels:
    tenant: io
    environment: ${ENVIRONMENT}
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/enforce-version: latest
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/audit-version: latest
    pod-security.kubernetes.io/warn: restricted
    pod-security.kubernetes.io/warn-version: latest
---
# Resource Quota per tenant namespace
apiVersion: v1
kind: ResourceQuota
metadata:
  name: io-${ENVIRONMENT}-quota
  namespace: io-${ENVIRONMENT}
spec:
  hard:
    requests.cpu: "${CPU_REQUESTS}"
    requests.memory: "${MEMORY_REQUESTS}"
    limits.cpu: "${CPU_LIMITS}"
    limits.memory: "${MEMORY_LIMITS}"
    persistentvolumeclaims: "${PVC_COUNT}"
    services: "${SERVICE_COUNT}"
    secrets: "${SECRET_COUNT}"
    configmaps: "${CONFIGMAP_COUNT}"
---
# Network Policy for tenant isolation
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: io-${ENVIRONMENT}-isolation
  namespace: io-${ENVIRONMENT}
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow ingress from same namespace
  - from:
    - namespaceSelector:
        matchLabels:
          name: io-${ENVIRONMENT}
  # Allow ingress from ingress controller
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
  # Allow ingress from monitoring
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
  egress:
  # Allow egress to same namespace
  - to:
    - namespaceSelector:
        matchLabels:
          name: io-${ENVIRONMENT}
  # Allow egress to kube-system (DNS)
  - to:
    - namespaceSelector:
        matchLabels:
          name: kube-system
  # Allow egress to external services
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
    - protocol: UDP
      port: 53
---
# RBAC for tenant namespace
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: io-${ENVIRONMENT}
  name: io-${ENVIRONMENT}-admin
rules:
- apiGroups: [""]
  resources: ["*"]
  verbs: ["*"]
- apiGroups: ["apps"]
  resources: ["*"]
  verbs: ["*"]
- apiGroups: ["networking.k8s.io"]
  resources: ["*"]
  verbs: ["*"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: io-${ENVIRONMENT}-admin-binding
  namespace: io-${ENVIRONMENT}
subjects:
- kind: ServiceAccount
  name: argocd-application-controller
  namespace: argocd
roleRef:
  kind: Role
  name: io-${ENVIRONMENT}-admin
  apiGroup: rbac.authorization.k8s.io
