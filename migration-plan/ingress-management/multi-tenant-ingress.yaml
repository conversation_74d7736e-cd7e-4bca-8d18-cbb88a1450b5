# Multi-tenant ingress configuration for all IO environments
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: io-multi-tenant-ingress
  namespace: ingress-nginx
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  ingressClassName: nginx
  tls:
  # Test Environment
  - hosts:
    - "api.test.iohealth.com"
    - "admin.test.iohealth.com"
    - "consumer.test.iohealth.com"
    - "provider.test.iohealth.com"
    secretName: io-test-tls
  # Demo Environment  
  - hosts:
    - "api.demo.iohealth.com"
    - "admin.demo.iohealth.com"
    - "consumer.demo.iohealth.com"
    - "provider.demo.iohealth.com"
    secretName: io-demo-tls
  # Staging Environment
  - hosts:
    - "api.stg.iohealth.com"
    - "admin.stg.iohealth.com"
    - "consumer.stg.iohealth.com"
    - "provider.stg.iohealth.com"
    secretName: io-stg-tls
  # Production Environment
  - hosts:
    - "api.iohealth.com"
    - "admin.iohealth.com"
    - "consumer.iohealth.com"
    - "provider.iohealth.com"
    secretName: io-prd-tls
  rules:
  # Test Environment Rules
  - host: "api.test.iohealth.com"
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: gateway
            port:
              number: 80
        # Route to test namespace
  - host: "admin.test.iohealth.com"
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: sehatuk-admin-ui
            port:
              number: 80
  # Demo Environment Rules
  - host: "api.demo.iohealth.com"
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: gateway
            port:
              number: 80
  # Staging Environment Rules  
  - host: "api.stg.iohealth.com"
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: gateway
            port:
              number: 80
  # Production Environment Rules
  - host: "api.iohealth.com"
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: gateway
            port:
              number: 80
