# Multi-tenant monitoring configuration for Prometheus

---
# ServiceMonitor for each tenant namespace
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: io-test-services
  namespace: io-test
  labels:
    tenant: io
    environment: test
spec:
  selector:
    matchLabels:
      tenant: io
      environment: test
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics

---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: io-demo-services
  namespace: io-demo
  labels:
    tenant: io
    environment: demo
spec:
  selector:
    matchLabels:
      tenant: io
      environment: demo
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics

---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: io-stg-services
  namespace: io-stg
  labels:
    tenant: io
    environment: stg
spec:
  selector:
    matchLabels:
      tenant: io
      environment: stg
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics

---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: io-prd-services
  namespace: io-prd
  labels:
    tenant: io
    environment: prd
spec:
  selector:
    matchLabels:
      tenant: io
      environment: prd
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics

---
# PrometheusRule for tenant-specific alerts
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: io-tenant-alerts
  namespace: monitoring
spec:
  groups:
  - name: io-tenant-alerts
    rules:
    - alert: IOTenantHighCPU
      expr: |
        (
          sum(rate(container_cpu_usage_seconds_total{namespace=~"io-.*"}[5m])) by (namespace)
          /
          sum(kube_resourcequota{namespace=~"io-.*", resource="requests.cpu"}) by (namespace)
        ) > 0.8
      for: 5m
      labels:
        severity: warning
        tenant: io
      annotations:
        summary: "High CPU usage in IO tenant namespace {{ $labels.namespace }}"
        description: "CPU usage is above 80% of quota in namespace {{ $labels.namespace }}"
    
    - alert: IOTenantHighMemory
      expr: |
        (
          sum(container_memory_usage_bytes{namespace=~"io-.*"}) by (namespace)
          /
          sum(kube_resourcequota{namespace=~"io-.*", resource="requests.memory"}) by (namespace)
        ) > 0.8
      for: 5m
      labels:
        severity: warning
        tenant: io
      annotations:
        summary: "High memory usage in IO tenant namespace {{ $labels.namespace }}"
        description: "Memory usage is above 80% of quota in namespace {{ $labels.namespace }}"
