apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: "gateway-application-set"
spec:
  goTemplate: true
  goTemplateOptions: [ "missingkey=error" ]

  generators:
    - list:
        elements:
            # All IO Applications now target single cluster
          - path: "overlays/io/test/gateway"
            clusterIP: "https://TARGET_CLUSTER_IP:6443"  # Single cluster IP
            namespace: "io-test"                          # Prefixed namespace
          - path: "overlays/io/demo/gateway"
            clusterIP: "https://TARGET_CLUSTER_IP:6443"  # Single cluster IP
            namespace: "io-demo"                          # Prefixed namespace
          - path: "overlays/io/stg/gateway"
            clusterIP: "https://TARGET_CLUSTER_IP:6443"  # Single cluster IP
            namespace: "io-stg"                           # Prefixed namespace
          - path: "overlays/io/prd/gateway"
            clusterIP: "https://TARGET_CLUSTER_IP:6443"  # Single cluster IP
            namespace: "io-prd"                           # Prefixed namespace

  template:
    metadata:
      name: 'gateway-{{ index (splitList "/" .path) 1  }}-{{ index (splitList "/" .path) 2  }}'
    spec:
      project: "default"

      source:
        path: "{{ .path }}"
        repoURL: "**************:my-workforce/Kubernetes.git"
        targetRevision: "iohealth-main"

      destination:
        namespace: '{{ .namespace }}'  # Use prefixed namespace
        server: '{{ .clusterIP }}'

      syncPolicy:
        syncOptions:
          - CreateNamespace=true
        automated:
          prune: true
          selfHeal: true
