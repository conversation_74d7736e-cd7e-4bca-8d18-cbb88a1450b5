# Java Environment Variables Conversion Guide

## Overview
Java services typically expect environment variables in UPPERCASE with underscores. This guide shows how to convert your current environment variables for Java services.

## Current vs Required Format

### Database Configuration
```yaml
# Current format (from environment.properties)
DB_HOST: "*********"
DB_USER: "dGVzdF9kYl91c2Vy"  # base64 encoded
DB_PASS: "WjJvNWFYaE9lbnBuUlVBITE1"  # base64 encoded

# Java service format (uppercase with underscores)
DATABASE_HOST: "*********"
DATABASE_USERNAME: "dGVzdF9kYl91c2Vy"
DATABASE_PASSWORD: "WjJvNWFYaE9lbnBuUlVBITE1"
```

### Keycloak Configuration
```yaml
# Current format
KEYCLOAK_REALM: "test-sehhati"
KEYCLOAK_GATEWAY_WEB_APP_CLIENT_SECRET: "WFRxTjl5RzFQMjFxcFU3VTRHV1NDZkhsTGZZQ0VUTnk="
KEYCLOAK_INTEGRATION_CLIENT_SECRET: "cUw5YkZWbTVwNXp2VnR4M3lLN2FpZkJSY3NGUFVYSzc="

# Java service format (already correct, but ensure consistency)
KEYCLOAK_REALM: "test-sehhati"
KEYCLOAK_GATEWAY_WEB_APP_CLIENT_SECRET: "WFRxTjl5RzFQMjFxcFU3VTRHV1NDZkhsTGZZQ0VUTnk="
KEYCLOAK_INTEGRATION_CLIENT_SECRET: "cUw5YkZWbTVwNXp2VnR4M3lLN2FpZkJSY3NGUFVYSzc="
```

### OCI/S3 Configuration
```yaml
# Current format
OCI_S3_REGION: "me-dubai-1"
OCI_S3_ENDPOINT: "axpebais8u12.compat.objectstorage.me-dubai-1.oraclecloud.com"
OCI_S3_ACCESS_KEY: "NWNlNDc5YTE1ZDY1Zjg0NjVkNGRmOWU5MWMwYWNiMDJhYWVlM2JlYw=="
OCI_S3_SECRET_KEY: "N2FJbkZWMWtLRHhMRlhVL3FjSjdORGtycElTRmJFUE5wRkpMdEhlZFhhYz0="

# Java service format
S3_REGION: "me-dubai-1"
S3_ENDPOINT: "axpebais8u12.compat.objectstorage.me-dubai-1.oraclecloud.com"
S3_ACCESS_KEY: "NWNlNDc5YTE1ZDY1Zjg0NjVkNGRmOWU5MWMwYWNiMDJhYWVlM2JlYw=="
S3_SECRET_KEY: "N2FJbkZWMWtLRHhMRlhVL3FjSjdORGtycElTRmJFUE5wRkpMdEhlZFhhYz0="
```

## Updated ConfigMap Template for Java Services

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: java-service-configmap
data:
  # Environment identification
  ENVIRONMENT: "$(ENV)"
  TENANT: "io"
  
  # Database configuration (Java format)
  DATABASE_HOST: "$(DB_HOST)"
  DATABASE_PORT: "3306"
  DATABASE_NAME: "$(ENV)_database"
  SPRING_DATASOURCE_URL: "********************************************"
  
  # Keycloak configuration
  KEYCLOAK_SERVER_URL: "https://api.$(ROOT_DOMAIN)/"
  KEYCLOAK_REALM: "$(KEYCLOAK_REALM)"
  KEYCLOAK_CLIENT_ID: "integration_client"
  
  # Spring Boot specific
  SPRING_PROFILES_ACTIVE: "$(ENV)"
  SPRING_KAFKA_BOOTSTRAP_SERVERS: "kafka-cluster-kafka-bootstrap.io-$(ENV):9092"
  
  # S3/Object Storage
  S3_REGION: "$(OCI_S3_REGION)"
  S3_ENDPOINT: "$(OCI_S3_ENDPOINT)"
  S3_BUCKET_NAME: "files.io-$(ENV)"
  
  # OpenSearch
  OPENSEARCH_HOST: "$(OPEN_SEARCH_HOST_NAME)"
  OPENSEARCH_PORT: "$(OPEN_SEARCH_PORT)"
  OPENSEARCH_SCHEME: "$(OPEN_SEARCH_HOST_SCHEMA)"
  OPENSEARCH_USERNAME: "$(OPEN_SEARCH_USERNAME)"
  
  # Service URLs (internal communication)
  GATEWAY_URL: "http://gateway.io-$(ENV)/graphql"
  USER_MANAGEMENT_URL: "http://user-management.io-$(ENV):8000/graphql/"
  NOTIFICATION_URL: "http://notification.io-$(ENV):8080"
  
  # Feature flags (uppercase)
  FEATURE_FLAG_ACTIVITY_TRACKER_ENABLED: "$(FEATURE_FLAG_ACTIVITY_TRACKER_FEATURE_ENABLED)"
  FEATURE_FLAG_MARKETPLACE_ENABLED: "$(FEATURE_FLAG_MARKETPLACE_FEATURE_ENABLED)"
  FEATURE_FLAG_PRESCRIPTION_ENABLED: "$(FEATURE_FLAG_PRESCRIPTION_FEATURE_ENABLED)"
  
  # Timezone
  TZ: "Asia/Dubai"
  
  # Logging
  LOGGING_LEVEL_ROOT: "INFO"
  LOGGING_LEVEL_COM_IOHEALTH: "DEBUG"
```

## Updated Secret Template for Java Services

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: java-service-secret
type: Opaque
data:
  # Database credentials (Java format)
  DATABASE_USERNAME: "$(DB_USER)"
  DATABASE_PASSWORD: "$(DB_PASS)"
  SPRING_DATASOURCE_USERNAME: "$(DB_USER)"
  SPRING_DATASOURCE_PASSWORD: "$(DB_PASS)"
  
  # Keycloak secrets
  KEYCLOAK_CLIENT_SECRET: "$(KEYCLOAK_INTEGRATION_CLIENT_SECRET)"
  KEYCLOAK_ADMIN_USERNAME: "$(KEYCLOAK_ADMIN)"
  KEYCLOAK_ADMIN_PASSWORD: "$(KEYCLOAK_ADMIN_PASSWORD)"
  
  # S3 credentials
  S3_ACCESS_KEY: "$(OCI_S3_ACCESS_KEY)"
  S3_SECRET_KEY: "$(OCI_S3_SECRET_KEY)"
  
  # OpenSearch credentials
  OPENSEARCH_PASSWORD: "$(OPEN_SEARCH_PASSWORD)"
  
  # SMTP credentials
  SMTP_USERNAME: "$(SMTP_USERNAME)"
  SMTP_PASSWORD: "$(SMTP_PASSWORD)"
  
  # API keys
  GOOGLE_API_KEY: "$(USER_MGMT_GOOGLE_API_KEY)"
  FLOWISE_USERNAME: "$(FLOWISE_USERNAME)"
  FLOWISE_PASSWORD: "$(FLOWISE_PASSWORD)"
```

## Service-Specific Environment Variable Mappings

### Gateway Service
```yaml
# Current variables that need conversion
NODE_ENV: "$(ENV)"  # Keep as is
KEYCLOAK_RSA_PUBLIC_KEY: "$(KEYCLOAK_RSA_PUBLIC_KEY)"  # Keep as is

# Add Java-friendly versions
ENVIRONMENT: "$(ENV)"
PUBLIC_KEY_PATH: "/keys/public-key.pem"
FORWARD_HEADERS: "Authorization,Accept-Language,User-Session-Id"
```

### User Management Service
```yaml
# Database
DATABASE_URL: "mysql://$(DB_USER):$(DB_PASS)@$(DB_HOST)/$(ENV)_usermanagement"
SPRING_DATASOURCE_URL: "**************************************************"

# Keycloak
KEYCLOAK_BASE_URL: "https://api.$(ROOT_DOMAIN)/auth"
KEYCLOAK_REALM_NAME: "$(KEYCLOAK_REALM)"
```

### AI Service
```yaml
# Database
AI_DATABASE_URL: "mysql://$(DB_USER):$(DB_PASS)@$(DB_HOST)/$(ENV)_ai"
SPRING_DATASOURCE_URL: "**************************************"

# External services
MWF_SERVER_URL: "https://gateway.myworkforce.ai"
FEDERATED_BACKEND_URL: "http://gateway.io-$(ENV)/graphql"
```

## Implementation Steps

### 1. Update Base ConfigMaps
For each Java service in `/base/*/config-map.yaml`:
- Convert environment variable names to UPPERCASE_WITH_UNDERSCORES
- Ensure consistency across all services
- Add service-specific variables as needed

### 2. Update Base Secrets
For each Java service in `/base/*/secret.yaml`:
- Convert secret names to UPPERCASE_WITH_UNDERSCORES
- Maintain backward compatibility during transition

### 3. Update Environment Configurations
In `/overlays/environment-configs/*/environment.properties`:
- Add Java-friendly variable names alongside existing ones
- Gradually phase out old variable names

### 4. Test Environment Variable Resolution
```bash
# Test that variables are properly resolved
kubectl exec -it <pod-name> -n io-test -- env | grep -E "(DATABASE|KEYCLOAK|S3)"
```

## Migration Strategy for Environment Variables

### Phase 1: Add New Variables (Backward Compatible)
- Add new UPPERCASE_WITH_UNDERSCORES variables alongside existing ones
- Update Java services to use new variables
- Keep old variables for compatibility

### Phase 2: Update Services
- Update each Java service to use new environment variable names
- Test thoroughly in test environment
- Deploy to other environments

### Phase 3: Remove Old Variables
- Once all services are updated, remove old variable names
- Clean up configuration files
- Update documentation
