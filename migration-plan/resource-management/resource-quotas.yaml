# Resource quotas for each IO environment namespace

---
# Test Environment - Lower resource allocation
apiVersion: v1
kind: ResourceQuota
metadata:
  name: io-test-quota
  namespace: io-test
spec:
  hard:
    requests.cpu: "20"
    requests.memory: "40Gi"
    limits.cpu: "40"
    limits.memory: "80Gi"
    persistentvolumeclaims: "50"
    services: "100"
    secrets: "50"
    configmaps: "50"
    pods: "200"

---
# Demo Environment - Medium resource allocation
apiVersion: v1
kind: ResourceQuota
metadata:
  name: io-demo-quota
  namespace: io-demo
spec:
  hard:
    requests.cpu: "30"
    requests.memory: "60Gi"
    limits.cpu: "60"
    limits.memory: "120Gi"
    persistentvolumeclaims: "75"
    services: "150"
    secrets: "75"
    configmaps: "75"
    pods: "300"

---
# Staging Environment - High resource allocation
apiVersion: v1
kind: ResourceQuota
metadata:
  name: io-stg-quota
  namespace: io-stg
spec:
  hard:
    requests.cpu: "50"
    requests.memory: "100Gi"
    limits.cpu: "100"
    limits.memory: "200Gi"
    persistentvolumeclaims: "100"
    services: "200"
    secrets: "100"
    configmaps: "100"
    pods: "500"

---
# Production Environment - Maximum resource allocation
apiVersion: v1
kind: ResourceQuota
metadata:
  name: io-prd-quota
  namespace: io-prd
spec:
  hard:
    requests.cpu: "100"
    requests.memory: "200Gi"
    limits.cpu: "200"
    limits.memory: "400Gi"
    persistentvolumeclaims: "200"
    services: "300"
    secrets: "150"
    configmaps: "150"
    pods: "1000"

---
# Limit Ranges for consistent resource allocation
apiVersion: v1
kind: LimitRange
metadata:
  name: io-test-limits
  namespace: io-test
spec:
  limits:
  - default:
      cpu: "500m"
      memory: "1Gi"
    defaultRequest:
      cpu: "100m"
      memory: "256Mi"
    type: Container
  - max:
      cpu: "4"
      memory: "8Gi"
    min:
      cpu: "50m"
      memory: "128Mi"
    type: Container
