# Consolidated shared secrets for multi-tenant cluster
# Each environment gets its own secret in its namespace

---
# Test Environment Secrets
apiVersion: v1
kind: Secret
metadata:
  name: shared-secrets
  namespace: io-test
type: Opaque
stringData:
  TENANT: "io"
  ENVIRONMENT: "test"
  KEYCLOAK_ADMIN: "master_admin"
  KEYCLOAK_ADMIN_PASSWORD: "admin"
  KEYCLOAK_ADMIN_CLI_USERNAME: "admin"
  KEYCLOAK_ADMIN_CLI_PASSWORD: "M7X8LT2G1@!RdG7nyu31zl059u"
  KEYCLOAK_GATEWAY_WEB_APP_CLIENT_SECRET: "CymcPmUUSyjf1qPws1DOWAxUGSldhGMf"
  KEYCLOAK_INTEGRATION_CLIENT_SECRET: "HvWx9cujFvA6lSui3Vv3yCi4K62LD6q5"
  DB_USER: "IO_Health"
  DB_PASS: "10@Le&19Het"
  OCI_S3_ACCESS_KEY: "minio-admin"
  OCI_S3_SECRET_KEY: "1P3NHA2f1y08eUhr"
  OPEN_SEARCH_PASSWORD: "admin_Admin_123"

---
# Demo Environment Secrets  
apiVersion: v1
kind: Secret
metadata:
  name: shared-secrets
  namespace: io-demo
type: Opaque
stringData:
  TENANT: "io"
  ENVIRONMENT: "demo"
  KEYCLOAK_ADMIN: "master_admin"
  KEYCLOAK_ADMIN_PASSWORD: "admin"
  # ... demo-specific secrets

---
# Staging Environment Secrets
apiVersion: v1
kind: Secret
metadata:
  name: shared-secrets
  namespace: io-stg
type: Opaque
stringData:
  TENANT: "io"
  ENVIRONMENT: "stg"
  KEYCLOAK_ADMIN: "master_admin"
  KEYCLOAK_ADMIN_PASSWORD: "admin"
  # ... staging-specific secrets

---
# Production Environment Secrets
apiVersion: v1
kind: Secret
metadata:
  name: shared-secrets
  namespace: io-prd
type: Opaque
stringData:
  TENANT: "io"
  ENVIRONMENT: "prd"
  KEYCLOAK_ADMIN: "master_admin"
  KEYCLOAK_ADMIN_PASSWORD: "admin"
  KEYCLOAK_ADMIN_CLI_USERNAME: "admin"
  KEYCLOAK_ADMIN_CLI_PASSWORD: "M7X8LT2G1@!RdG7nyu31zl059u"
  KEYCLOAK_GATEWAY_WEB_APP_CLIENT_SECRET: "CymcPmUUSyjf1qPws1DOWAxUGSldhGMf"
  KEYCLOAK_INTEGRATION_CLIENT_SECRET: "HvWx9cujFvA6lSui3Vv3yCi4K62LD6q5"
  DB_USER: "IOHealth_Login"
  DB_PASS: "10H3@lT#25"
  OCI_S3_ACCESS_KEY: "minio-admin"
  OCI_S3_SECRET_KEY: "2pDyep5bW4Q3gcd8"
  OPEN_SEARCH_PASSWORD: "admin_Admin_123"
