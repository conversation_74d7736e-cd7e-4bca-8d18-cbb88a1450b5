# Updated ConfigMap template for cross-namespace service discovery
apiVersion: v1
kind: ConfigMap
metadata:
  name: gateway-configmap
data:
  # Updated service URLs to include namespace in FQDN format
  URL_0: "http://prescription.io-${ENV}:9090/graphql"
  URL_1: "http://user-management.io-${ENV}:8000/graphql/"
  URL_2: "http://health-program.io-${ENV}:8000/graphql"
  URL_3: "http://terminology.io-${ENV}:8081/graphql"
  URL_4: "http://decision-maker.io-${ENV}:8085/graphql"
  URL_5: "http://integration-gateway.io-${ENV}:8033/graphql"
  URL_6: "http://bulk.io-${ENV}:8077/graphql"
  URL_7: "http://referral.io-${ENV}:8074/graphql"
  URL_8: "http://eclaim-sync.io-${ENV}:7060/graphql"
  URL_9: "http://activity-tracker.io-${ENV}:3010/graphql"
  URL_10: "http://payment.io-${ENV}:8089/graphql/"
  URL_11: "http://subscription.io-${ENV}:7030/graphql/"
  URL_12: "http://survey.io-${ENV}:9060/graphql"
  URL_13: "http://ai.io-${ENV}:8000/graphql/"
  URL_14: "http://optima-agent.io-${ENV}:5090/graphql"
  URL_15: "http://flowise-integration.io-${ENV}:3060/graphql"
  URL_16: "http://marketplace.io-${ENV}:9070/graphql"
  URL_17: "http://knowledge-hub.io-${ENV}:5080/graphql"
  URL_18: "http://digital-twin-ai.io-${ENV}:5010/graphql"
  URL_19: "http://optima.io-${ENV}:7050/graphql"
  URL_20: "http://medical-edits.io-${ENV}:5060/graphql"
  URL_21: "http://virtual-gateway.io-${ENV}:4040/graphql"
  URL_22: "http://payer-integration-api.io-${ENV}:7010/graphql"
  URL_23: "http://vendor-integration-api.io-${ENV}:7020/graphql"
  
  # Kafka references need namespace updates
  SPRING_KAFKA_BOOTSTRAP_SERVERS: "kafka-cluster-kafka-bootstrap.io-${ENV}:9092"
  
  # Database connections remain the same (external)
  CORES: "https://admin.$(ROOT_DOMAIN),https://gcadmin.$(ROOT_DOMAIN),https://consumer.$(ROOT_DOMAIN),https://provider.$(ROOT_DOMAIN),https://optima.$(ROOT_DOMAIN),https://pt-profile.$(ROOT_DOMAIN)"
  PUBLIC_KEY_PATH: "/keys/public-key.pem"
  FORWARD_HEADERS: "Authorization,Accept-Language,User-Session-Id"
  NODE_ENV: "$(NODE_ENV)"
  public-key.pem: "$(KEYCLOAK_RSA_PUBLIC_KEY)"
