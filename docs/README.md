# IOHealth System Knowledge Base

## Overview
IOHealth is a comprehensive guided care journey software platform consisting of 20+ microservices, multiple React-based UI clients, and mobile applications. The system is deployed on Oracle Cloud Infrastructure (OCI) using Kubernetes (OKE clusters).

## System Architecture

### Infrastructure Overview
- **Development Environments**: 2 VM-based environments with Docker
- **Cloud Environments**: Multiple OKE clusters (Enhanced) on OCI
- **Central Management**: Standalone ArgoCD cluster for GitOps deployment
- **Database**: MySQL Database System on OCI per environment
- **Registry**: OCI Container Registry for all images
- **DNS**: Cloudflare for public domain management

### Cluster Specifications
- **Worker Nodes**: 3 nodes per cluster
- **Node Specs**: 4 OCPU, 64GB RAM each
- **Database**: 2-4 ECPUs, 16-32GB RAM (non-prod), higher for production

## Repository Structure

```
/
├── argocd/           # ArgoCD ApplicationSets for GitOps deployment
├── base/             # Base Kubernetes manifests for all components
├── overlays/         # Environment-specific configurations
├── components/       # Reusable Kustomize components
├── secrets/          # Shared secrets (to be improved)
├── docs/             # This knowledge base
└── migration-plan/   # Multi-tenant migration planning
```

## Component Categories

### 1. Core Business Services (Java-based GraphQL)
- Gateway (API Gateway/GraphQL Federation)
- User Management (Python-based)
- Health Program
- Prescription
- Payment
- Referral
- Decision Maker
- Act Tracker
- File ivityService
- Marketplace
- Knowledge Hub
- Medical Edits
- Notification
- Survey
- Subscription
- Terminology
- Bulk Operations
- Integration Services (API, Gateway, eClaim)
- Payer Integration API
- Vendor Integration API

### 2. AI/ML Services
- AI Service
- AI Assistant
- AI Companion
- Digital Twin
- Digital Twin AI
- Flowise Integration
- Optima (AI-powered optimization)
- Optima Agent
- Optima Guide
- Optima Mock

### 3. Infrastructure Services
- Keycloak (Identity & Access Management)
- Kafka (Message Streaming)
- OpenSearch (Search & Analytics)
- Temporal (Workflow Engine)
- Zipkin (Distributed Tracing)
- Redis (Caching)
- MinIO (Object Storage)

### 4. UI Applications (React-based)
- SehatUK UI (Multiple variants: Admin, Consumer, Provider, etc.)
- Various specialized UIs for different user roles

### 5. Integration & Mock Services
- IO Hub (FHIR Integration)
- IO Payer
- IO Companion
- Various Mock Services (Optima, Enaya, Medgulf)
- HDS (Health Data Service)
- SADA (Streaming/Media service)

### 6. DevOps & Monitoring
- Kube Prometheus (Monitoring Stack)
- Ingress Nginx (Load Balancing)
- Reloader (Configuration Reloading)
- OpenVPN (Secure Access)
- Docker Registry Secrets

## Key Dependencies & Relationships

### Database Dependencies
- Most Java services → MySQL Database
- Environment-specific database schemas
- Shared database credentials per environment

### Authentication Flow
- All services → Keycloak for authentication
- Gateway → Validates tokens from Keycloak
- UI Applications → Keycloak for SSO

### Message Flow
- Services → Kafka for async communication
- Health Program → Kafka consumers
- Notification service → Kafka for events

### Search & Analytics
- Multiple services → OpenSearch for search functionality
- Knowledge Hub → OpenSearch for content indexing
- User Management → OpenSearch for user data

### File Storage
- File Service → OCI Object Storage (S3-compatible)
- SADA → Object storage for media files
- Various services → File Service for document management

## Current Challenges (To Address During Migration)

1. **Secret Management**: Inconsistent secret handling across environments
2. **Configuration Chaos**: Some configurations not properly organized
3. **Service Dependencies**: Complex interdependencies need mapping
4. **Resource Management**: No proper resource quotas/limits
5. **Multi-tenancy**: Currently separate clusters per environment

## Migration Goals

1. **Consolidate Infrastructure**: Move from 4 separate OKE clusters to 1 multi-tenant cluster
2. **Improve Security**: Implement proper RBAC, network policies, secret management
3. **Optimize Resources**: Better resource utilization and cost reduction
4. **Standardize Configurations**: Clean up and standardize all configurations
5. **Enhance Monitoring**: Implement comprehensive observability

## Documentation Structure

- `components/` - Detailed analysis of each component
- `dependencies/` - Service dependency mapping
- `environments/` - Environment-specific configurations
- `architecture/` - System architecture diagrams and flows
- `migration/` - Migration planning and execution guides
- `troubleshooting/` - Common issues and solutions

## Next Steps

1. Complete component analysis and documentation
2. Map all service dependencies
3. Create migration plan with proper tenant isolation
4. Implement improved secret management
5. Execute phased migration with testing at each step
