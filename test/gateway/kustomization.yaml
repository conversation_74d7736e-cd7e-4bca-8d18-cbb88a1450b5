apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: test

resources:
- ../../../../base/gateway
- ../../../environment-configs/med-gulf-test

components:
  - ../../../../components/remove-hpa-and-resources

images:
  - name: gateway
    newName: dxb.ocir.io/axpebais8u12/apollo
    newTag: sehatuk.release.zipkin.0.4

configMapGenerator:
  - name: gateway-configmap
    behavior: replace
    envs:
      - gateway-configmap

patches:
  - target:
      kind: ConfigMap
      name: shared-vars
      version: v1
      group: ""
    patch: |-
      - op: replace
        path: /metadata/name
        value: shared-vars-a8