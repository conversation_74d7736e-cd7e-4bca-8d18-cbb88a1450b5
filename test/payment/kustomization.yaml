apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: test

resources:
- ../../../../base/payment
- ../../../environment-configs/med-gulf-test

components:
- ../../../../components/remove-hpa-and-resources

images:
- name: payment
  newName: dxb.ocir.io/axpebais8u12/payment
  newTag: sehatuk.release.0.0.160

# Hyper-Pay Gateway
configMapGenerator:
- behavior: merge
  literals:
  - hyper-pay.url="https://eu-test.oppwa.com/v1/"
  - hyper-pay.token="OGFjN2E0Yzk4YmExODk2MzAxOGJhNDAzODhlODAyZDN8dzlxR3lXY1pibnhSczhxWA=="
  - hyper-pay.entity-id="8ac7a4c98ba18963018ba4041d6e02d7"
  name: payment-configmap

patches:
- patch: |-
    - op: replace
      path: /metadata/name
      value: shared-vars-c8
  target:
    kind: ConfigMap
    name: shared-vars
    version: v1
