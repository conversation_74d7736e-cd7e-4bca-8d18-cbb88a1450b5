apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-ai-assistant-route
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "50m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: '600'
    nginx.ingress.kubernetes.io/proxy-send-timeout: '600'
    nginx.ingress.kubernetes.io/proxy-read-timeout: '600'
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/rewrite-target:  /$2
    nginx.ingress.kubernetes.io/use-forwarded-headers: "true"
    nginx.ingress.kubernetes.io/server-snippet: |
      set_real_ip_from 10.0.0.0/16;
      if ($http_x_forwarded_proto = http) {
        return 301 https://$server_name$request_uri;
      }

spec:
  rules:
    - host: "api.$(ROOT_DOMAIN)"
      http:
        paths:
          - path: /ai-assistant(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: ai-assistant
                port:
                  number: 8000