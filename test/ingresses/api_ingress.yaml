apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-route
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "50m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: '600'
    nginx.ingress.kubernetes.io/proxy-send-timeout: '600'
    nginx.ingress.kubernetes.io/proxy-read-timeout: '600'
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/rewrite-target:  /$2
    nginx.ingress.kubernetes.io/use-forwarded-headers: "true"
    nginx.ingress.kubernetes.io/server-snippet: |
      proxy_set_header User-Session-Id $http_user_session_id;
      set_real_ip_from 10.0.0.0/16;
      if ($http_x_forwarded_proto = http) {
        return 301 https://$server_name$request_uri;
      }

spec:
  rules:
    - host: "api.$(ROOT_DOMAIN)"
      http:
        paths:
          - path: /(integration)/(api/delivery_url?.*)
            pathType: Prefix
            backend:
              service:
                name: integration
                port:
                  number: 8087
          - path: /(terminology-app)/(api/icon/?.*)
            pathType: Prefix
            backend:
              service:
                name: terminology
                port:
                  number: 8081
          - path: /file-service(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: file
                port:
                  number: 8079
          - path: /notification(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: notification
                port:
                  number: 8081
          - path: /subscription(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: subscription
                port:
                  number: 7030
          - path: /(gateway)/(graphql?.*)
            pathType: Prefix
            backend:
              service:
                name: gateway
                port:
                  number: 80
          - path: /integration-api(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: integration-api
                port:
                  number: 8078
          - path: /payer-integration-api(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: payer-integration-api
                port:
                  number: 7010
          - path: /provider-integration-api(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: vendor-integration-api
                port:
                  number: 7020
          - path: /temporal-web(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: temporal-web
                port:
                  number: 8080
          - path: /integration-gateway(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: integration-gateway
                port:
                  number: 8033
          - path: /_app(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: temporal-web
                port:
                  number: 8080
          - path: /optima(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: optima
                port:
                  number: 7050
          - path: /optima_agent(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: optima-agent
                port:
                  number: 5090
          - path: /optima_mock(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: optima-mock
                port:
                  number: 6080
          - path: /virtual-gateway(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: virtual-gateway
                port:
                  number: 4040
          - path: /ai-companion(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: ai-companion
                port:
                  number: 8000