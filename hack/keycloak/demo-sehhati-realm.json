{"id": "********-a400-455b-92c5-4fd36443317e", "realm": "demo-se<PERSON><PERSON>", "notBefore": 0, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 1728000, "accessTokenLifespanForImplicitFlow": 1728000, "ssoSessionIdleTimeout": 432000, "ssoSessionMaxLifespan": 432000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 86400, "clientSessionMaxLifespan": 86400, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 86400, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 10800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": false, "duplicateEmailsAllowed": false, "resetPasswordAllowed": true, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "e865f425-7d83-4c5c-8181-f7a2c17df293", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "********-a400-455b-92c5-4fd36443317e", "attributes": {}}, {"id": "5b52c15b-f58f-472e-8e58-79d4d09f4723", "name": "ROLE_USER", "description": "Jhipster user role", "composite": false, "clientRole": false, "containerId": "********-a400-455b-92c5-4fd36443317e", "attributes": {}}, {"id": "3c307b7c-c246-4b2b-92bc-0095cb735e69", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "********-a400-455b-92c5-4fd36443317e", "attributes": {}}, {"id": "cf7cf5d8-20aa-4563-9ac4-a1d0d4294976", "name": "ROLE_ADMIN", "description": "Jhipster administrator role", "composite": false, "clientRole": false, "containerId": "********-a400-455b-92c5-4fd36443317e", "attributes": {}}, {"id": "6dd04c86-5d1a-47d9-bade-a665ff76d2fd", "name": "default-roles-demo-se<PERSON><PERSON>", "description": "${role_default-roles}", "composite": true, "composites": {"realm": ["offline_access", "uma_authorization"], "client": {"account": ["manage-account", "view-profile"]}}, "clientRole": false, "containerId": "********-a400-455b-92c5-4fd36443317e", "attributes": {}}], "client": {"realm-management": [{"id": "a97c3244-5da1-408a-a7f1-89a9b4138a50", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "a75a4549-7896-45fc-9b95-88c7bff0f698", "attributes": {}}, {"id": "6a8f0d6f-1cfe-45f8-a427-8f50681c1e7a", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "a75a4549-7896-45fc-9b95-88c7bff0f698", "attributes": {}}, {"id": "afe6c8a8-3b23-401b-aafb-784a407ec68f", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "a75a4549-7896-45fc-9b95-88c7bff0f698", "attributes": {}}, {"id": "1923d6bd-591b-4457-9124-3e4342b9b63c", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "a75a4549-7896-45fc-9b95-88c7bff0f698", "attributes": {}}, {"id": "61a5aa4a-03ec-4fd9-97b3-e055883a7066", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "a75a4549-7896-45fc-9b95-88c7bff0f698", "attributes": {}}, {"id": "4673d4da-9134-4721-b322-051dd31199db", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-users", "query-groups"]}}, "clientRole": true, "containerId": "a75a4549-7896-45fc-9b95-88c7bff0f698", "attributes": {}}, {"id": "7ee55a38-04e0-40d3-b887-729140945c2b", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "a75a4549-7896-45fc-9b95-88c7bff0f698", "attributes": {}}, {"id": "a0ea763b-dc52-4124-b033-1c224d104807", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "a75a4549-7896-45fc-9b95-88c7bff0f698", "attributes": {}}, {"id": "7e32542a-7a5c-424d-85f6-ac0feb6bb048", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "a75a4549-7896-45fc-9b95-88c7bff0f698", "attributes": {}}, {"id": "3eda74f9-16d4-4284-b62d-adb54e6d909f", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "a75a4549-7896-45fc-9b95-88c7bff0f698", "attributes": {}}, {"id": "c973a478-b711-4ee4-9f59-ef37d6dbed7e", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "a75a4549-7896-45fc-9b95-88c7bff0f698", "attributes": {}}, {"id": "e69b6933-93a3-4ddc-9543-6af206e26529", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "a75a4549-7896-45fc-9b95-88c7bff0f698", "attributes": {}}, {"id": "0de90161-af68-494f-a42b-ec90e295411b", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "a75a4549-7896-45fc-9b95-88c7bff0f698", "attributes": {}}, {"id": "d66d9405-fdb5-46ed-be4d-62921b7a397f", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "a75a4549-7896-45fc-9b95-88c7bff0f698", "attributes": {}}, {"id": "f6b0e6d7-9c22-47a2-a18a-3f8505abc160", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "a75a4549-7896-45fc-9b95-88c7bff0f698", "attributes": {}}, {"id": "b13502e0-bd8b-40f2-9971-448761e1f66d", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "a75a4549-7896-45fc-9b95-88c7bff0f698", "attributes": {}}, {"id": "b3fe3e23-beb5-4ea4-9c87-011c3e9b8b12", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "a75a4549-7896-45fc-9b95-88c7bff0f698", "attributes": {}}, {"id": "ae0ac36e-e2ec-4a13-8a6a-f99b8b9e06e3", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "a75a4549-7896-45fc-9b95-88c7bff0f698", "attributes": {}}, {"id": "e0e2dfe8-de49-4c49-b242-a96974f42466", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["manage-users", "view-realm", "manage-identity-providers", "manage-authorization", "query-realms", "view-users", "impersonation", "view-identity-providers", "manage-events", "manage-clients", "view-authorization", "create-client", "query-users", "view-clients", "manage-realm", "query-clients", "view-events", "query-groups"]}}, "clientRole": true, "containerId": "a75a4549-7896-45fc-9b95-88c7bff0f698", "attributes": {}}], "integration_client": [], "security-admin-console": [], "vendor": [], "admin": [], "admin-cli": [], "gateway_web_app": [{"id": "6501ceaf-2aa0-4f9a-82d1-8a25d36df773", "name": "upload_code_system_file", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "bdfb3079-6956-4bd4-a50a-84d007d8b359", "name": "view_users", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "7358f8ba-e62a-44e1-a6c0-48ef17531449", "name": "view_feature_rating", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "0203bb9c-0b43-4c73-9d4d-d42364d17558", "name": "manage_branches", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "9e17fc3a-c01c-4fb8-b490-cb657ffbdc7e", "name": "view_wallets", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "fe2d5811-06f5-4069-ad47-e68362fc74c6", "name": "manage_validation_requests", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "b0d858c1-31d7-4c18-8db1-00297304b0ba", "name": "view_complaints", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "7fe8cc54-d211-46e8-a435-d87be2c67e4b", "name": "view_orders", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "d4b74f5b-7bab-48e3-90ee-4474fad0cc49", "name": "manage_out_patient_journeys", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "187d7ea9-cbe2-462d-9a0c-5f4737fcf193", "name": "view_customers", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "ef01bfe5-95bd-4c81-9cbf-17d727b143b9", "name": "publish_code_system", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "d94c1535-33c8-46e3-be32-86f4106ab8fe", "name": "manage_health_symptoms", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "5a4b2799-4c6b-44d0-a7dc-fdca9ae703e5", "name": "manage_optima_payer_credentials", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "f0aff418-76a9-4125-8ecb-2855ae8c4687", "name": "manage_payers", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "622f2ef6-e8ab-4abb-acbe-a6a4d4e52e43", "name": "manage_medical_forms", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "83693994-c599-4a42-81d6-725fbac7fe24", "name": "view_staff", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "3fe2ccd2-32be-4a50-be67-a4adf3a8360e", "name": "manage_code_system_lists", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "d808c607-1b60-4e38-99ec-3e47a53acda6", "name": "manage_payments", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "7736be85-5aa3-4bf4-b9ba-f9d2645c2b6d", "name": "manage_qualifications", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "4e07eca8-cf3f-4a9d-a00c-6901caf9a927", "name": "manage_health_programs", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "6332e165-1961-4d97-b709-42dd055b1bdd", "name": "view_subscriptions", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "7ea5615f-5520-486b-8787-265b9bc02e17", "name": "view_surveys", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "52f1d3c9-f131-4c0a-b548-15d371a89f0e", "name": "manage_health_package_orders", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "38f085b9-b597-4edd-9cee-370d7711c71e", "name": "send_bulk_messages", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "3341d65a-0b31-48d3-8ca4-2d5d2c843e0b", "name": "approve_health_packages", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "5f6b054e-74b6-4f3b-8c0d-521a18cc95ed", "name": "manage_chat_flow", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "042c5b0a-54c0-4159-a04f-2da61cfd7b5d", "name": "manage_invoices", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "ddb787ef-231a-4e38-8fc3-04f70b640b85", "name": "manage_pricing_rules", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "7c821d78-d91d-4840-925b-01d12774898c", "name": "manage_programs", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "f06f6be3-60a1-4b5a-8a83-bad25bffede0", "name": "manage_users", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "0b98f962-2bc5-4e9f-a66c-a8069111de56", "name": "manage_rejection_reasons", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "a690b4af-7c74-446d-ba4f-6eddf614eb13", "name": "validate_procedures", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "cf08a5b8-58ea-4045-b0e3-b19da0dcd48b", "name": "manage_risk_stratification", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "c12ea62e-1ab4-49bb-8153-48cd5e07d4b1", "name": "manage_insurance_networks", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "eeb6774d-279f-4145-8de4-b8d49adcd380", "name": "manage_marketplace_orders", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "dcfcb1ea-6ac5-4c51-b6fc-0210d1bb8862", "name": "manage_permission_group", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "79d3cc0a-c9ad-4466-8671-fd9944cd5090", "name": "manage_admin_promotions", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "a4b52b63-99ff-4603-997a-d851a292f61d", "name": "manage_specializations", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "903f9f73-7cff-409a-9055-cd356050b0ae", "name": "manage_delivery_time_slots", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "5990c338-1db3-4c44-8d7d-2fe99246586d", "name": "manage_discounts", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "e4fdb34e-ac93-4b43-bbb8-41d6b5a47451", "name": "manage_wallets", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "3ad305f9-d76f-4ac5-990b-12869d64f63f", "name": "approve_products", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "abb9f23d-39b1-4473-afba-2832273f32b8", "name": "manage_health_packages", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "9e094861-258b-48e7-b500-6a942e5493ce", "name": "manage_edits_and_actions", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "0393b771-20d1-4133-860b-57172610b324", "name": "manage_brands", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "91b60332-5576-4218-94da-e7ee8a558950", "name": "manage_social_and_streaming", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "010cc0d8-ac12-426c-8bc0-3eb5eb4ae1c8", "name": "view_patient_enrollment_request", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "6792b807-0edb-4e6d-acdc-cece95194130", "name": "manage_insurance", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "3ccd1106-2d7b-4374-8c81-43bc3fd6725f", "name": "manage_patients", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "de1bca6f-1c17-441b-ade8-6a068a87fb79", "name": "manage_product_categories", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "8be3a51a-6efc-4a4d-a568-e32787d37c5d", "name": "assign_prescription_order_to_vendors", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "245bde9a-9971-4acb-9b03-5f638bf71967", "name": "manage_settings", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "13f623c1-9875-4f65-97e3-b6113b6bd803", "name": "ADMIN", "description": "", "composite": true, "composites": {"realm": ["ROLE_ADMIN"], "client": {"realm-management": ["manage-users", "view-realm", "view-clients"], "gateway_web_app": ["USER"]}}, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "3cad7116-7c5b-4052-bcb5-b030a66ffe0e", "name": "view_health_programs", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "507f6025-3ec4-45ff-b4be-8957ad3e229c", "name": "manage_visits", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "5dca5db9-ef8e-482e-9170-1ffc314d0ec1", "name": "manage_appointments", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "580abc86-5954-401a-9f0f-034a555b2736", "name": "manage_shipping", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "c2231f49-0117-41c5-a09a-f9adb55dfe55", "name": "manage_agent_definitions", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "a516cbeb-3078-481b-a898-2d6412f01302", "name": "manage_chat", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "ae57024c-6a65-400a-8609-67fc5de8ea4c", "name": "review_health_messages", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "8662f487-2e6c-4fca-a929-2b4c26e9541a", "name": "view_transaction", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "5fad8864-24a9-4fe2-84d8-9388a05dc99c", "name": "manage_parameters", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "c38ad102-524d-44a9-927b-25695bbe2ca3", "name": "workflow_manage_orders", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "13d6957a-9abb-40e6-81d8-5984e5044826", "name": "manage_program_templates", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "7e4d6b05-d2d2-48c7-a2fd-d676db8b6a1b", "name": "manage_dashboard", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "dc4fc117-8552-4b45-8cf9-34774b8bb5ac", "name": "manage_languages", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "908c6a8b-6ca1-43e6-a0bd-a47b69b2bad4", "name": "manage_medical_necessity", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "0dc06298-bd18-4835-8d9f-e4effbc61787", "name": "manage_rule_engine_rules", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "715a17d8-82ba-45db-abd3-512ef8e091ed", "name": "manage_diagnosis", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "0143cffc-7528-4ebe-bdb6-66e22717f540", "name": "manage_patient_medical_history", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "38913853-ed03-4181-8193-7c15d4a717fb", "name": "manage_visit_summary", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "d5dfd361-9f54-496d-be80-463087940341", "name": "view_code_system_lists", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "8a38ac22-f407-4d65-8f43-59fee761f6fa", "name": "manage_medical_edits", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "110c032c-aa51-4abf-b08a-c204933a0da0", "name": "manage_translations", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "3c0f8179-30a6-4a90-90b7-f77596a684c3", "name": "view_patients", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "3d1d5a4d-65ee-4c65-9a56-17d15e1ff077", "name": "manage_promotions", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "ae933b42-dbb9-4bf8-bbb2-19fb608a9a73", "name": "manage_cities", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "c367b56d-07a2-4b89-a250-6878b39c99a4", "name": "manage_health_conditions", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "f2a3c322-6a81-4538-a0ea-721d857121a1", "name": "manage_conference_configuration", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "6f0d8fbd-2934-413a-93f7-abceb03cfbf6", "name": "manage_products", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "e4747b33-047b-45f8-8085-8efe6c07d0cc", "name": "manage_blocks", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "2f20138b-84f8-4d53-8d9a-68d4f7271d46", "name": "view_visits", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "2cd3aa28-e189-4af4-b38c-08ed27c8c437", "name": "manage_health_channels", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "24ec77e1-39ca-4347-812c-80f1d23e0375", "name": "manage_activity_tracker", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "db2965db-652f-43e7-a60c-927af03cd657", "name": "manage_visit_cancel_reasons", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "d55aa295-c47a-42e7-80e7-9fd7ab<PERSON><PERSON>f", "name": "manage_articles", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "62f28bab-8053-4705-89fc-584d18de4ea0", "name": "manage_doctors", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "b150753a-df1b-434c-90f6-fec1c9046892", "name": "manage_callbacks_requests", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "572268a6-00f7-4dfb-8201-7a5e454c30ca", "name": "manage_subscriptions", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "670be9d6-3c00-4a8b-a3bd-a4b0768faf86", "name": "verify_national_ids", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "6a2df1e5-7b1e-401b-a227-2047040c9753", "name": "manage_departments", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "9ad69f78-a552-4450-9947-0e0cc544eb88", "name": "manage_medications", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "70b11833-b217-45a6-9009-91eec6949efe", "name": "manage_staff", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "626f9164-d0da-41a5-a830-966fa77dffc1", "name": "impersonate_user", "description": "impersonate_user", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "5e339c66-fea4-4fab-a418-d5020beece00", "name": "manage_orders", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "b3acd3a7-ca16-48bb-80fd-c0fa65f69200", "name": "manage_guided_care_patients", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "73b9bb84-38fe-4df5-9125-1d328da7aa5d", "name": "manage_program_teams", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "b4eb5cae-a57b-41a4-873c-893fe5a5d8c3", "name": "manage_early_refill_reasons", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "b7d4d139-0e0d-43b2-83ca-c7b4cc6cdf40", "name": "customer_support_manage_orders", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "377d4604-2e27-4314-9153-5535c91a49aa", "name": "manage_prescriptions", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "ef5358e7-da7a-45be-9cf5-6ff7ae7f817f", "name": "view_articles", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "5ed38e8f-1bb2-4ca0-8d2a-4e3467c99d7a", "name": "view_health_messages", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "69f630be-f1ec-4b9f-bf7c-cd0c0666d734", "name": "manage_case_management", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "28dda09b-005a-4b54-90cf-6edb187bfbe0", "name": "manage_health_messages", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "37bf4443-8b28-4e0f-831b-032138ce3cdb", "name": "medication_scientific_details", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "928a9837-d3c2-479d-9177-bba6d2521ad5", "name": "convert_virtual_order_to_real_one", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "a98393e0-a945-4dcc-ba24-995d631f92b3", "name": "manage_pharmacy_credentials", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "6509a685-2880-4e12-9b1a-50aadb8a6021", "name": "manage_health_package_categories", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "3fd35070-3bce-448d-b747-03d5585cc236", "name": "manage_scientific_details", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "2071f9ae-ce51-4f8c-8ba5-6a9527bb8ce7", "name": "manage_visit_rejection_reasons", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "1c44fdc1-23e1-4836-87e0-51b1f1dd8cb2", "name": "USER", "description": "", "composite": true, "composites": {"realm": ["ROLE_USER", "offline_access", "uma_authorization"], "client": {"account": ["manage-account", "view-profile"]}}, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "35dd447b-7d2f-4c7d-966e-949c61d52c48", "name": "review_articles", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "e922d5f5-2cd9-4b63-bdba-8777f8931978", "name": "view_programs", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "5e2f00e5-1e7e-4ea5-b18c-ce7ae29fbc3d", "name": "manage_code_system_reviewers", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "f7930c29-2335-4d5e-9a79-4fbaf1b3a8bd", "name": "customer_team_cancel_item", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "133cb835-b85c-43ad-8998-41e1821aba54", "name": "manage_plugins", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "bd072526-5fe5-447e-bc6f-f32eaa1b00e6", "name": "view_appointments", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "c2a29d49-7473-4a02-a0c6-3fda255840af", "name": "view_program_templates", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "ada8c4e8-fea5-4251-ab01-346e974c5fad", "name": "manage_medical_delivery_requests", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "2b059d8f-2d2f-4d00-b09c-6e6a71f56f04", "name": "manage_checkouts", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "e5523fca-723f-4f62-9f28-401fa9d00922", "name": "manage_surveys", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "4558318f-4de6-4b94-befc-dfa815e852b0", "name": "manage_vendors", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "1a22e4c3-a41d-4db5-8b3c-0c052e7cc399", "name": "view_program_teams", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "5e3752fc-1f1c-463e-8e01-952cfc60aafe", "name": "manage_health_channels_categories", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "cc438da8-3cbd-4371-89de-89f2588ff2cf", "name": "manage_labs", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "5cf24d41-fab4-4dd9-8620-e1a73d11dd01", "name": "manage_chat_flow_contributer", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "2925fd7c-cb23-422e-9683-c33565953a67", "name": "manage_health_programs_care_for_fields", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "7e072d0f-31a6-4b37-8081-22e5271f0811", "name": "manage_code_system_editor_reviewers", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}, {"id": "713396c0-9597-4584-9888-39d2c39bb141", "name": "verify_member_ids", "composite": false, "clientRole": true, "containerId": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "attributes": {}}], "account-console": [], "broker": [{"id": "933a1ab7-b702-4053-a45d-08930ccc8ee4", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "7f137fc4-84e9-43ba-afa3-d8bf9b299d17", "attributes": {}}], "account": [{"id": "ba17154e-5fe4-4145-bcce-27299a6bc390", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "0e15a8d7-402b-43d0-bf09-91b26a318c6e", "attributes": {}}, {"id": "eb8cc42e-51ae-4fe3-ba8e-754cf7ba6f88", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "0e15a8d7-402b-43d0-bf09-91b26a318c6e", "attributes": {}}, {"id": "50f8cbcb-5798-4fb8-9b03-1f367b3b0f0d", "name": "view-groups", "description": "${role_view-groups}", "composite": false, "clientRole": true, "containerId": "0e15a8d7-402b-43d0-bf09-91b26a318c6e", "attributes": {}}, {"id": "5399d686-5352-4084-8af6-4fc8e4d7ea86", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "0e15a8d7-402b-43d0-bf09-91b26a318c6e", "attributes": {}}, {"id": "574e8f5a-2901-4bc1-856f-6a061100b463", "name": "delete-account", "description": "${role_delete-account}", "composite": false, "clientRole": true, "containerId": "0e15a8d7-402b-43d0-bf09-91b26a318c6e", "attributes": {}}, {"id": "ea114786-870f-4cd5-b735-8a26d2d0b232", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "0e15a8d7-402b-43d0-bf09-91b26a318c6e", "attributes": {}}, {"id": "5fdf4d8a-ec55-40f9-a273-23311d2912bc", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "0e15a8d7-402b-43d0-bf09-91b26a318c6e", "attributes": {}}, {"id": "578b065c-4f06-4712-b1bd-651db811c9f8", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "0e15a8d7-402b-43d0-bf09-91b26a318c6e", "attributes": {}}], "digital_twin": [], "customer": []}}, "groups": [{"id": "e76d0315-21f9-4982-912e-a815745a111c", "name": "000", "path": "/000", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"gateway_web_app": ["view_patient_enrollment_request"]}}, {"id": "4bd1fa0c-8409-40a8-975f-f125db43b9f4", "name": "Admins", "path": "/Admins", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {}}, {"id": "2995fdda-ac4f-4723-8e34-eaada329fb2f", "name": "Users", "path": "/Users", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {}}, {"id": "cc76c8b0-44e4-4c6f-ba0c-b963d2d71b89", "name": "articles", "path": "/articles", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"gateway_web_app": ["view_articles"]}}, {"id": "11111427-18fa-4a32-b685-95bb5f4af1cb", "name": "can manage blocks", "path": "/can manage blocks", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"gateway_web_app": ["manage_blocks"]}}, {"id": "67da53f2-b0a2-45c8-af7b-dd1c2f9ea2a4", "name": "can manage cities", "path": "/can manage cities", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"gateway_web_app": ["manage_cities"]}}, {"id": "3dedd531-8aac-4788-93a2-d37a77f1ccfe", "name": "can manage cities + blocks", "path": "/can manage cities + blocks", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"gateway_web_app": ["manage_cities", "manage_blocks"]}}, {"id": "792fe2de-3f76-4a11-acb5-991738c57785", "name": "can manage time slots", "path": "/can manage time slots", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"gateway_web_app": ["manage_delivery_time_slots"]}}, {"id": "25295024-e13b-4649-b507-86b137333a76", "name": "can view orders", "path": "/can view orders", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"gateway_web_app": ["view_orders"]}}, {"id": "9c4c2af6-4a9b-4117-9942-96659faf79e0", "name": "case manager", "path": "/case manager", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"gateway_web_app": ["manage_case_management"]}}, {"id": "454409bb-fd37-4e73-9b9f-c69a4f5976f9", "name": "cs-tm", "path": "/cs-tm", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"gateway_web_app": ["customer_team_cancel_item", "view_appointments", "view_visits"]}}, {"id": "c669844a-d0be-411d-a86b-7c838bddd245", "name": "dental_hygienist", "path": "/dental_hygienist", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"gateway_web_app": ["view_customers", "view_patients", "view_programs"]}}, {"id": "a66cdc15-6dc6-49d2-8af7-45beb5fe5d4a", "name": "dhic user", "path": "/dhic user", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"gateway_web_app": ["view_users", "view_program_teams", "manage_program_templates", "view_programs"]}}, {"id": "a7853d3c-eebf-4e03-82a3-bc86cfe55121", "name": "diabetes_educator", "path": "/diabetes_educator", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"gateway_web_app": ["view_customers", "view_patients", "view_programs"]}}, {"id": "0060d3c1-809c-416d-b8bd-5b508a011eeb", "name": "doctor", "path": "/doctor", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"gateway_web_app": ["view_customers", "validate_procedures", "manage_guided_care_patients", "view_patients", "view_programs", "verify_member_ids", "manage_dashboard", "view_patient_enrollment_request"]}}, {"id": "610cb6a6-4c87-48c0-b77a-908c5e07f896", "name": "fitness_coach", "path": "/fitness_coach", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"gateway_web_app": ["view_customers", "view_patients", "view_programs"]}}, {"id": "7ddeff95-8b62-464c-853c-7356cb860238", "name": "manage case managment", "path": "/manage case managment", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"gateway_web_app": ["manage_case_management"]}}, {"id": "d236593f-b312-4530-8fd5-c7c553ef213a", "name": "manage settings", "path": "/manage settings", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"gateway_web_app": ["manage_settings"]}}, {"id": "689c7aca-8eea-41a1-bcc9-98210f4c2717", "name": "nurse", "path": "/nurse", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"gateway_web_app": ["view_customers", "manage_guided_care_patients", "view_patients", "manage_prescriptions", "manage_visits", "manage_appointments", "manage_dashboard"]}}, {"id": "38e97735-13d1-436f-9b9d-fb89a41ec357", "name": "nutritionist", "path": "/nutritionist", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"gateway_web_app": ["view_customers", "view_patients", "view_programs"]}}, {"id": "803f8863-b7b4-4b92-a470-d014e532bfc6", "name": "optometrist", "path": "/optometrist", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"gateway_web_app": ["view_customers", "view_patients", "view_programs"]}}, {"id": "243e1dca-b71e-404c-b028-ba6731f05455", "name": "patient", "path": "/patient", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"gateway_web_app": ["view_users"]}}, {"id": "1029fff7-8ceb-42cb-a57f-9a0e69eec2bc", "name": "payer super admin", "path": "/payer super admin", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"gateway_web_app": ["manage_visit_summary", "validate_procedures", "medication_scientific_details", "view_health_programs", "manage_health_programs", "view_program_templates", "manage_medications", "view_program_teams", "manage_visit_rejection_reasons", "manage_health_channels", "manage_chat", "view_customers", "manage_invoices", "manage_health_channels_categories", "manage_programs", "manage_health_programs_care_for_fields", "manage_users", "manage_visit_cancel_reasons", "manage_diagnosis"]}}, {"id": "fb4d767c-ea71-4efe-84b5-cf46774d135d", "name": "payer user", "path": "/payer user", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"gateway_web_app": ["manage_health_channels", "manage_visit_summary", "view_customers", "validate_procedures", "manage_health_channels_categories", "view_health_programs", "manage_health_programs", "manage_health_programs_care_for_fields"]}}, {"id": "926db709-0ea5-4006-9fc4-8e04f4378593", "name": "pharmacist", "path": "/pharmacist", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"gateway_web_app": ["view_customers", "manage_orders", "manage_marketplace_orders", "manage_medical_delivery_requests", "manage_prescriptions"]}}, {"id": "d522f80f-7149-4240-b903-2da6418a7f22", "name": "podiatric_medical_assistant", "path": "/podiatric_medical_assistant", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"gateway_web_app": ["view_customers", "view_patients", "view_programs"]}}, {"id": "9c60cbdd-aae5-474c-a8d5-71829286ac1d", "name": "psychologist", "path": "/psychologist", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"gateway_web_app": ["view_customers", "view_patients", "view_programs"]}}, {"id": "b8525bfa-dda1-4e50-9003-65ac986af01a", "name": "rcm", "path": "/rcm", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"gateway_web_app": ["view_transaction", "manage_validation_requests"]}}, {"id": "9d453d63-2425-4f6b-b8cb-e3bffecc695f", "name": "receptionist", "path": "/receptionist", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"gateway_web_app": ["view_customers", "manage_health_package_orders", "manage_visits", "manage_appointments"]}}, {"id": "d1686444-3e16-4cd6-a05d-599a38f9b48e", "name": "social_worker", "path": "/social_worker", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"gateway_web_app": ["view_customers", "view_patients", "view_programs"]}}, {"id": "52f3e5ff-07c5-4b0f-aab8-fc47cf7234cf", "name": "vendor super admin", "path": "/vendor super admin", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"gateway_web_app": ["validate_procedures", "manage_promotions", "manage_marketplace_orders", "manage_branches", "manage_conference_configuration", "manage_pharmacy_credentials", "manage_products", "manage_validation_requests", "view_orders", "manage_delivery_time_slots", "manage_discounts", "view_customers", "manage_health_packages", "view_programs", "manage_optima_payer_credentials", "manage_medical_forms", "manage_doctors", "manage_settings", "manage_medical_delivery_requests", "manage_health_package_orders", "manage_checkouts", "manage_visits", "manage_appointments", "manage_departments", "manage_chat_flow", "manage_chat", "manage_guided_care_patients", "manage_invoices", "view_transaction", "manage_program_teams", "manage_prescriptions", "manage_users", "manage_dashboard"]}}, {"id": "2012de9b-7bc9-47d0-bf33-a4149191b0c7", "name": "view health messages", "path": "/view health messages", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"gateway_web_app": ["view_health_messages"]}}], "defaultRole": {"id": "6dd04c86-5d1a-47d9-bade-a665ff76d2fd", "name": "default-roles-demo-se<PERSON><PERSON>", "description": "${role_default-roles}", "composite": true, "clientRole": false, "containerId": "********-a400-455b-92c5-4fd36443317e"}, "requiredCredentials": ["password"], "passwordPolicy": "digits(1) and lowerCase(1) and upperCase(1) and maxLength(32) and length(8) and notEmail(undefined) and notUsername(undefined)", "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpPolicyCodeReusable": false, "otpSupportedApplications": ["totpAppFreeOTPName", "totpAppGoogleName", "totpAppMicrosoftAuthenticatorName"], "localizationTexts": {}, "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyExtraOrigins": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "webAuthnPolicyPasswordlessExtraOrigins": [], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account", "view-groups"]}]}, "clients": [{"id": "0e15a8d7-402b-43d0-bf09-91b26a318c6e", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/demo-sehhati/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/demo-sehhati/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "9a3037b9-5179-4f30-8c82-407a7a780da3", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/demo-sehhati/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/demo-sehhati/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "6bc97e53-0ef2-4410-8ad1-8149062ac48f", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "de76b154-ee7b-44ee-a595-17f79b666b6d", "clientId": "admin", "name": "", "description": "", "rootUrl": "https://admin.dev.iohealth.com", "adminUrl": "https://admin.dev.iohealth.com", "baseUrl": "https://admin.dev.iohealth.com", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["http://localhost:5004", "*", "http://localhost:5003"], "webOrigins": ["https://admin.dev.iohealth.com", "https://gcadmin.dev.iohealth.com", "http://localhost:5004", "http://localhost:5003"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "oauth2.device.authorization.grant.enabled": "false", "backchannel.logout.revoke.offline.tokens": "true", "use.refresh.tokens": "true", "oidc.ciba.grant.enabled": "false", "backchannel.logout.session.required": "true", "client_credentials.use_refresh_token": "false", "acr.loa.map": "{}", "require.pushed.authorization.requests": "false", "tls.client.certificate.bound.access.tokens": "false", "display.on.consent.screen": "false", "token.response.type.bearer.lower-case": "false"}, "authenticationFlowBindingOverrides": {"direct_grant": "1c78fdcf-1dc3-4932-88ab-96f4859a71e5", "browser": "acb553ea-9781-44f8-9ae1-5cbd0e8b3994"}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "0508138d-20a9-41ad-a694-be057946e789", "name": "permissions", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "multivalued": "true", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "permissions", "jsonType.label": "String", "usermodel.clientRoleMapping.clientId": "gateway_web_app"}}, {"id": "84fe701e-df63-43ae-aae7-bb1e9afcd303", "name": "user_id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"aggregate.attrs": "false", "introspection.token.claim": "true", "multivalued": "false", "userinfo.token.claim": "true", "user.attribute": "user_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "user_id", "jsonType.label": "long"}}, {"id": "ba7542b6-8f6e-49ef-acdb-ea501f954324", "name": "app_role", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "app_role", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "app_role", "jsonType.label": "String"}}, {"id": "15a3cd13-da8e-429b-9f1f-66ac2bc5a2bf", "name": "national_id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "national_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "national_id", "jsonType.label": "String"}}, {"id": "f96620d0-3c6f-4aaf-ba4f-686b08b1edf4", "name": "app_type", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "app_type", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "app_type", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "9572eaef-b270-45a0-9161-dd6e96786342", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "7f137fc4-84e9-43ba-afa3-d8bf9b299d17", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "********-db93-4edb-9e30-9c88b7d3da24", "clientId": "customer", "name": "", "description": "", "rootUrl": "https://client.dev.iohealth.com", "adminUrl": "https://client.dev.iohealth.com", "baseUrl": "https://client.dev.iohealth.com", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["*"], "webOrigins": ["*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"access.token.lifespan": "21600", "post.logout.redirect.uris": "+", "oauth2.device.authorization.grant.enabled": "false", "backchannel.logout.revoke.offline.tokens": "false", "use.refresh.tokens": "true", "oidc.ciba.grant.enabled": "false", "backchannel.logout.session.required": "true", "client_credentials.use_refresh_token": "false", "acr.loa.map": "{}", "require.pushed.authorization.requests": "false", "tls.client.certificate.bound.access.tokens": "false", "display.on.consent.screen": "false", "token.response.type.bearer.lower-case": "false"}, "authenticationFlowBindingOverrides": {"direct_grant": "1c78fdcf-1dc3-4932-88ab-96f4859a71e5", "browser": "acb553ea-9781-44f8-9ae1-5cbd0e8b3994"}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "a571b5f1-fc31-4083-8837-0cb906aa27ff", "name": "patient_id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "patient_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "patient_id", "jsonType.label": "long"}}, {"id": "aed9c0ed-05ac-42ae-b214-132434a3bd13", "name": "user_id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "user_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "user_id", "jsonType.label": "long"}}, {"id": "9b1dfcb4-a027-4c22-9d94-fd03f471d0c2", "name": "app_role", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "app_role", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "app_role", "jsonType.label": "String"}}, {"id": "95a9895f-5fe0-4925-9704-ca0985daea2d", "name": "app_type", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "app_type", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "app_type", "jsonType.label": "String"}}, {"id": "f1d3c6d0-226e-477c-a953-2be274a935cc", "name": "national_id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "national_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "national_id", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "********-edff-4306-84ae-9ada706c0fe6", "clientId": "digital_twin", "name": "", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["*"], "webOrigins": ["*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"oidc.ciba.grant.enabled": "false", "post.logout.redirect.uris": "*", "oauth2.device.authorization.grant.enabled": "false", "backchannel.logout.session.required": "true", "backchannel.logout.revoke.offline.tokens": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "eebaf3ab-c3bd-4978-abea-537f95fa8d40", "clientId": "gateway_web_app", "name": "", "description": "", "rootUrl": "${authBaseUrl}", "adminUrl": "${authBaseUrl}", "baseUrl": "/realms/demo-sehhati/gateway_web_app", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "CymcPmUUSyjf1qPws1DOWAxUGSldhGMf", "redirectUris": ["*"], "webOrigins": ["*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": true, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"oidc.ciba.grant.enabled": "false", "client.secret.creation.time": "**********", "backchannel.logout.session.required": "true", "post.logout.redirect.uris": "+", "display.on.consent.screen": "false", "oauth2.device.authorization.grant.enabled": "false", "use.jwks.url": "true", "backchannel.logout.revoke.offline.tokens": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "6dde34c1-571c-493c-aa41-b52c164c167e", "name": "vendor_id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "vendor_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "vendor_id", "jsonType.label": "long"}}, {"id": "7d3f0ce3-36fa-4c01-905f-729d9f5ce0eb", "name": "patient_id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "patient_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "patient_id", "jsonType.label": "long"}}, {"id": "82c300d5-f6c9-425d-902d-383f9c62d8c4", "name": "lang<PERSON><PERSON>", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "lang<PERSON><PERSON>", "id.token.claim": "false", "access.token.claim": "false", "claim.name": "lang<PERSON><PERSON>", "jsonType.label": "String"}}, {"id": "6ad3a922-4a4c-42ca-884c-043bb4a45d68", "name": "doctor_id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "doctor_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "doctor_id", "jsonType.label": "long"}}, {"id": "f86d9320-aaa0-4e58-8c3f-27fd42774869", "name": "app_role", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "app_role", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "app_role", "jsonType.label": "String"}}, {"id": "9ad52c7d-43fb-43b1-8563-e161a9c39c31", "name": "driver_id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "driver_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "driver_id", "jsonType.label": "long"}}, {"id": "ecd26c62-12ea-481d-9acc-4dc428702783", "name": "login", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "preferred_username", "id.token.claim": "false", "access.token.claim": "false", "claim.name": "login", "jsonType.label": "String"}}, {"id": "477d8a52-693c-440b-a9f3-c0f2565f0d12", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "8694bb3f-17b5-4cfc-8958-84eeecbec84f", "name": "client_roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"usermodel.clientRoleMapping.rolePrefix": "ROLE_", "introspection.token.claim": "true", "multivalued": "true", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "roles", "jsonType.label": "String", "usermodel.clientRoleMapping.clientId": "gateway_web_app"}}, {"id": "a0c3c79b-4444-421e-ba76-3cfdc2015a3a", "name": "user_id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "user_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "user_id", "jsonType.label": "long"}}, {"id": "74a161bf-1ef7-4fcd-a23b-d06def97af7f", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "f61909a2-37a9-464a-8716-81d50a0c6d2c", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "a6be8a7b-f3f0-4871-9333-d0a0e1c90d86", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "74834b6d-8725-423d-9ae4-e91d6f778fbb", "name": "mobile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "mobile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "mobile", "jsonType.label": "String"}}, {"id": "51d9c45c-8793-4dc1-868e-4bc23cafc2d6", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "introspection.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "792c5aa0-f773-4493-9426-8d472212c8bd", "name": "app_type", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "app_type", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "app_type", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "583e699f-333d-4e75-a75e-4c78419a5e09", "clientId": "integration_client", "name": "Dev integration", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "HvWx9cujFvA6lSui3Vv3yCi4K62LD6q5", "redirectUris": ["/*"], "webOrigins": ["/*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"oidc.ciba.grant.enabled": "false", "oauth2.device.authorization.grant.enabled": "false", "client.secret.creation.time": "**********", "backchannel.logout.session.required": "true", "backchannel.logout.revoke.offline.tokens": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "7f22c9b7-e456-4007-87b7-56d68046eae9", "name": "user_id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "user_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "user_id", "jsonType.label": "long"}}, {"id": "832d767f-9dbe-468d-8e48-ba8f59adb8fd", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "introspection.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String"}}, {"id": "ad78a8d2-f0b8-4f37-abb0-b01faafff8df", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "introspection.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String"}}, {"id": "68b2d8ac-8fb1-4a21-bb0e-acbf8e53c469", "name": "app_role", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "app_role", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "app_role", "jsonType.label": "String"}}, {"id": "518abed1-3a13-48d4-b835-945b083b1120", "name": "app_type", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "app_type", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "app_type", "jsonType.label": "String"}}, {"id": "04278b2b-95e5-4d34-990e-f30709094c91", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "client_id", "introspection.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "client_id", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "a75a4549-7896-45fc-9b95-88c7bff0f698", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "authorizationServicesEnabled": true, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"], "authorizationSettings": {"allowRemoteResourceManagement": false, "policyEnforcementMode": "ENFORCING", "resources": [{"name": "idp.resource.d7fb98f8-0e2d-4115-8307-77c01f51a0a2", "type": "IdentityProvider", "ownerManagedAccess": false, "attributes": {}, "_id": "2bd4468f-20f4-431b-9a8f-70b5249fdb82", "uris": [], "scopes": [{"name": "token-exchange"}]}, {"name": "client.resource.********-db93-4edb-9e30-9c88b7d3da24", "type": "Client", "ownerManagedAccess": false, "attributes": {}, "_id": "4cac326e-b3f3-494b-962e-029c5482cb7f", "uris": [], "scopes": [{"name": "view"}, {"name": "map-roles-client-scope"}, {"name": "configure"}, {"name": "map-roles"}, {"name": "manage"}, {"name": "map-roles-composite"}, {"name": "token-exchange"}]}, {"name": "idp.resource.26edfb06-4cdd-4157-a37b-d1b3ca57d8c4", "type": "IdentityProvider", "ownerManagedAccess": false, "attributes": {}, "_id": "7fca43e2-416e-4bf2-a090-19b4b5ed7e97", "uris": [], "scopes": [{"name": "token-exchange"}]}, {"name": "idp.resource.7840c6bf-a71b-4c07-8dd1-9338290e41cf", "type": "IdentityProvider", "ownerManagedAccess": false, "attributes": {}, "_id": "df0e8098-2f26-4cc0-b647-43dc96136104", "uris": [], "scopes": [{"name": "token-exchange"}]}], "policies": [{"id": "b068bf0a-9c1f-4d85-a5a0-298b180e36a7", "name": "customer-exchange-policy", "description": "", "type": "client", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"clients": "[\"customer\"]"}}, {"id": "0a774fad-4460-41ca-b4b3-21232f7edcd4", "name": "manage.permission.client.********-db93-4edb-9e30-9c88b7d3da24", "type": "scope", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"client.resource.********-db93-4edb-9e30-9c88b7d3da24\"]", "scopes": "[\"manage\"]"}}, {"id": "589e42c8-4d4c-4a9e-a6c7-12895370dd83", "name": "map-roles-composite.permission.client.********-db93-4edb-9e30-9c88b7d3da24", "type": "scope", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"client.resource.********-db93-4edb-9e30-9c88b7d3da24\"]", "scopes": "[\"map-roles-composite\"]"}}, {"id": "5a94e10a-52c9-4ff5-bcfc-37bbf23d8c4b", "name": "token-exchange.permission.idp.d7fb98f8-0e2d-4115-8307-77c01f51a0a2", "description": "", "type": "scope", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"idp.resource.d7fb98f8-0e2d-4115-8307-77c01f51a0a2\"]", "scopes": "[\"token-exchange\"]", "applyPolicies": "[\"customer-exchange-policy\"]"}}, {"id": "83d0f713-7b65-42b8-86c5-37668a2a5d7b", "name": "token-exchange.permission.idp.7840c6bf-a71b-4c07-8dd1-9338290e41cf", "description": "", "type": "scope", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"idp.resource.7840c6bf-a71b-4c07-8dd1-9338290e41cf\"]", "scopes": "[\"token-exchange\"]", "applyPolicies": "[\"customer-exchange-policy\"]"}}, {"id": "84340e4b-b9bc-4f07-8852-275bf99f6440", "name": "map-roles.permission.client.********-db93-4edb-9e30-9c88b7d3da24", "type": "scope", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"client.resource.********-db93-4edb-9e30-9c88b7d3da24\"]", "scopes": "[\"map-roles\"]"}}, {"id": "cdfc3111-96de-40ba-9406-36733f6bd18a", "name": "view.permission.client.********-db93-4edb-9e30-9c88b7d3da24", "type": "scope", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"client.resource.********-db93-4edb-9e30-9c88b7d3da24\"]", "scopes": "[\"view\"]"}}, {"id": "e9be0051-a55c-48e2-9d8c-60d1e69ef307", "name": "token-exchange.permission.client.********-db93-4edb-9e30-9c88b7d3da24", "type": "scope", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"client.resource.********-db93-4edb-9e30-9c88b7d3da24\"]", "scopes": "[\"token-exchange\"]"}}, {"id": "eaa3d1f0-1fb2-47d7-858d-371c9c8045c5", "name": "map-roles-client-scope.permission.client.********-db93-4edb-9e30-9c88b7d3da24", "type": "scope", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"client.resource.********-db93-4edb-9e30-9c88b7d3da24\"]", "scopes": "[\"map-roles-client-scope\"]"}}, {"id": "f142e42f-d579-45d5-b247-26ca5de97d45", "name": "token-exchange.permission.idp.26edfb06-4cdd-4157-a37b-d1b3ca57d8c4", "description": "", "type": "scope", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"idp.resource.26edfb06-4cdd-4157-a37b-d1b3ca57d8c4\"]", "scopes": "[\"token-exchange\"]", "applyPolicies": "[\"customer-exchange-policy\"]"}}, {"id": "f6e25748-7e25-45a6-a883-d0ef2e06cb3e", "name": "configure.permission.client.********-db93-4edb-9e30-9c88b7d3da24", "type": "scope", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"client.resource.********-db93-4edb-9e30-9c88b7d3da24\"]", "scopes": "[\"configure\"]"}}], "scopes": [{"id": "23e48656-3bdb-4281-91ba-90bfef36d863", "name": "view"}, {"id": "33191482-a60c-44fa-a13d-af4a892fb698", "name": "configure"}, {"id": "49cd5404-35f3-4c36-8f49-5cb3cc811fe3", "name": "map-roles-client-scope"}, {"id": "6451cb6b-f189-4e9e-9fb7-f6ee4cb00a3f", "name": "manage"}, {"id": "64c6b79e-6ad7-4941-8481-d78436e1b991", "name": "map-roles-composite"}, {"id": "77c4a25a-4b4f-4411-bfd0-14af52d18053", "name": "token-exchange"}, {"id": "92ab1ecd-f25e-487e-a40f-034af1ce2a76", "name": "map-roles"}], "decisionStrategy": "UNANIMOUS"}}, {"id": "9a3ccf34-bb65-4676-a5dd-4091635a3102", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/demo-sehhati/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/admin/demo-sehhati/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "17846c87-5f36-4459-8d40-99e9e2f6a135", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "da1d7382-dd2e-46e0-a25d-1da7b6cb6862", "clientId": "vendor", "name": "", "description": "", "rootUrl": "https://provider.dev.iohealth.com", "adminUrl": "https://provider.dev.iohealth.com", "baseUrl": "https://provider.dev.iohealth.com", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["*"], "webOrigins": ["*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "oauth2.device.authorization.grant.enabled": "false", "backchannel.logout.revoke.offline.tokens": "false", "use.refresh.tokens": "true", "oidc.ciba.grant.enabled": "false", "backchannel.logout.session.required": "true", "client_credentials.use_refresh_token": "false", "acr.loa.map": "{}", "require.pushed.authorization.requests": "false", "tls.client.certificate.bound.access.tokens": "false", "display.on.consent.screen": "false", "token.response.type.bearer.lower-case": "false"}, "authenticationFlowBindingOverrides": {"direct_grant": "1c78fdcf-1dc3-4932-88ab-96f4859a71e5", "browser": "acb553ea-9781-44f8-9ae1-5cbd0e8b3994"}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "4f57bd72-9fbe-4cfe-ad43-87d749ee5ef0", "name": "manager_id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "manager_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "manager_id", "jsonType.label": "long"}}, {"id": "f6abe042-a742-4346-99da-bdfe57b2400a", "name": "nutritionist_id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "nutritionist_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nutritionist_id", "jsonType.label": "long"}}, {"id": "0561487f-f712-488b-889d-bdbc62771f83", "name": "vendor_id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "vendor_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "vendor_id", "jsonType.label": "long"}}, {"id": "bc7afc69-8970-4593-8b03-3b7c5498f14b", "name": "national_id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "national_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "national_id", "jsonType.label": "String"}}, {"id": "22b9db85-7cb3-48b3-bb56-de9a96845657", "name": "doctor_id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "doctor_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "doctor_id", "jsonType.label": "long"}}, {"id": "3a600d51-dd34-487d-8d4f-2532e9a2d509", "name": "podiatric_medical_assistant_id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "podiatric_medical_assistant_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "podiatric_medical_assistant_id", "jsonType.label": "long"}}, {"id": "48ef8d2d-a2f6-420f-ae5c-13ab3a25ab2e", "name": "psychologist_id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "psychologist_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "psychologist_id", "jsonType.label": "long"}}, {"id": "5711ba3b-63e4-444e-b3f1-cf13c17654c2", "name": "app_type", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "app_type", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "app_type", "jsonType.label": "String"}}, {"id": "d2696ad9-ce88-437a-ab6a-c216ed10e2b3", "name": "pharmacist_id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "pharmacist_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "pharmacist_id", "jsonType.label": "long"}}, {"id": "0f495705-ee53-4206-b0dd-56075ca16663", "name": "dental_hygienist_id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "dental_hygienist_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "dental_hygienist_id", "jsonType.label": "long"}}, {"id": "8f544f17-2183-48fd-b416-9693b7e6b067", "name": "fitness_coach_id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "fitness_coach_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "fitness_coach_id", "jsonType.label": "long"}}, {"id": "ebca5ede-100b-4784-8344-196971960c70", "name": "app_role", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "app_role", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "app_role", "jsonType.label": "String"}}, {"id": "2a61b62c-44cd-4d09-be87-b9bfbf4f17ce", "name": "receptionist_id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "receptionist_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "receptionist_id", "jsonType.label": "long"}}, {"id": "3ef258fd-c4b0-4c97-82ed-b1424601b547", "name": "permissions", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "multivalued": "true", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "permissions", "jsonType.label": "String", "usermodel.clientRoleMapping.clientId": "gateway_web_app"}}, {"id": "9dad73e6-e45a-401a-a042-36245838453e", "name": "diabetes_educator_id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "diabetes_educator_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "diabetes_educator_id", "jsonType.label": "long"}}, {"id": "16a80d78-6513-4ff9-82a0-ccbe1976155f", "name": "nurse_id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "nurse_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nurse_id", "jsonType.label": "long"}}, {"id": "394b55e8-65a9-4f53-bf47-fb2f7b54b4b0", "name": "social_worker_id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "social_worker_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "social_worker_id", "jsonType.label": "long"}}, {"id": "5c98bb2a-8adf-453b-9f13-4da37ae15181", "name": "branches", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "multivalued": "true", "userinfo.token.claim": "true", "user.attribute": "branches", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "branches", "jsonType.label": "long"}}, {"id": "974b8212-09c3-47f2-988a-ac74427dbed2", "name": "user_id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "user_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "user_id", "jsonType.label": "long"}}, {"id": "1c5e2697-7329-44ac-924c-54182e716951", "name": "optometrist_id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "optometrist_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "optometrist_id", "jsonType.label": "long"}}, {"id": "ffdab547-0d1a-434c-962b-72616e9c3646", "name": "vendor_user_type", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "false", "userinfo.token.claim": "false", "user.attribute": "vendor_user_type", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "vendor_user_type", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "clientScopes": [{"id": "58b4324d-95ae-49d9-b1c2-9606b99e22d9", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "54ab0c11-4b85-40df-b54e-2ae2de847754", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "04d368c4-8712-4d5f-b3c7-79306d20d0d4", "name": "acr", "description": "OpenID Connect scope for add acr (authentication context class reference) to the token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "a42efacb-b3f1-497c-a2c2-d8885e5658e8", "name": "acr loa level", "protocol": "openid-connect", "protocolMapper": "oidc-acr-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "introspection.token.claim": "true"}}]}, {"id": "771c18d5-0bd6-4482-8829-73836624043a", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${profileScopeConsentText}"}, "protocolMappers": [{"id": "68777f47-6710-438a-819d-4a6163189727", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "7b424972-3eec-47a2-b5ea-983dbb7c62b8", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "long"}}, {"id": "29e6d7ee-4888-47e9-8759-444bc52c668a", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}, {"id": "f117d849-d981-4d4b-9166-6622b6e54855", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}, {"id": "a6e76958-a19e-4079-abbb-4aae7af5f6b6", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "introspection.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "690dab8a-831b-4d0f-91d7-7933297fcaaa", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "ccd82025-46c9-4e5d-951a-bdebbc8ce78c", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "e8c0ca25-886e-48a5-80d8-1f80790df4ec", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}, {"id": "cab1dd9f-519f-4af1-8046-2351dbad1ffb", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "bf540962-89d1-46d3-9c04-d870ba0cc68e", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}, {"id": "1077f197-335e-4f24-9fd8-4cfef3869e68", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "0ac4afab-d45d-4741-8dd9-2fdbd50db77e", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}, {"id": "7a46c556-f7ba-4acc-b965-8230ce9c1e16", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "b4833911-5fcf-4410-9a49-cb35719269e7", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}]}, {"id": "0d1071b1-ed08-4ed2-806d-ae6ab07e7c01", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${addressScopeConsentText}"}, "protocolMappers": [{"id": "dbaac153-4863-4dd9-ae6c-0f5b5d1c0e00", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "introspection.token.claim": "true", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "12eae309-b530-4b44-9394-78e57f61b632", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "10a4113e-4d81-41d5-a9f0-b7a9cf904029", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String"}}, {"id": "989cd55f-40f6-4c33-8abe-5060e2cb3377", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "multivalued": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}]}, {"id": "804a21a0-94c1-4060-b0b1-7f9675bb6a0a", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false", "consent.screen.text": ""}, "protocolMappers": [{"id": "c2cbd3f3-fa79-45d6-bf4d-1ea37b815dd2", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {"access.token.claim": "true", "introspection.token.claim": "true"}}]}, {"id": "d816ae14-4655-4c12-aeef-3a716e25a924", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "true", "consent.screen.text": "${rolesScopeConsentText}"}, "protocolMappers": [{"id": "a39f482c-5cf7-4cb8-b996-37954654b9bb", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "multivalued": "true", "user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String"}}, {"id": "7356e4d0-e2eb-43b6-848c-582abe20d7be", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "multivalued": "true", "user.attribute": "foo", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String"}}, {"id": "07e364c8-e5f8-4a9d-ac9b-492a970b2a7e", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {"access.token.claim": "true", "introspection.token.claim": "true"}}]}, {"id": "808250c8-ff8d-4ae9-bb83-bb4b2b30ba8f", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${emailScopeConsentText}"}, "protocolMappers": [{"id": "9efbbd85-792f-4856-a36e-0e8e5b6c2dae", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "3c275d59-34b7-4787-b695-e8753b05bf41", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}]}, {"id": "debdb0f4-9249-49c4-a908-3782455bb03e", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${phoneScopeConsentText}"}, "protocolMappers": [{"id": "e9a639e6-3158-4b48-8cc5-a0c501ca4425", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}, {"id": "44009242-a4fe-4cab-91b0-9ca2f8ca7717", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}]}, {"id": "e20f5f16-700d-483e-8d6f-a6f55d6ba18f", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}], "defaultDefaultClientScopes": ["acr", "role_list", "profile", "web-origins", "email", "roles"], "defaultOptionalClientScopes": ["address", "microprofile-jwt", "phone", "offline_access"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "referrerPolicy": "no-referrer", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=********; includeSubDomains"}, "smtpServer": {}, "loginTheme": "<PERSON><PERSON><PERSON>-theme", "accountTheme": "", "adminTheme": "", "emailTheme": "", "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "identityProviders": [{"alias": "uae_pass", "displayName": "UAE Pass", "internalId": "26edfb06-4cdd-4157-a37b-d1b3ca57d8c4", "providerId": "oidc", "enabled": true, "updateProfileFirstLoginMode": "on", "trustEmail": true, "storeToken": true, "addReadTokenRoleOnCreate": true, "authenticateByDefault": false, "linkOnly": false, "firstBrokerLoginFlowAlias": "UAE Pass", "postBrokerLoginFlowAlias": "UAE Pass", "config": {"acceptsPromptNoneForwardFromClient": "false", "tokenUrl": "https://stg-id.uaepass.ae/idshub/token", "isAccessTokenJWT": "false", "jwksUrl": "https://api.dev.iohealth.com/auth/realms/demo-sehhati/protocol/openid-connect/certs", "filteredByClaim": "false", "backchannelSupported": "false", "loginHint": "false", "clientAuthMethod": "client_secret_post", "syncMode": "IMPORT", "clientSecret": "sandbox_stage", "allowedClockSkew": "0", "guiOrder": "0", "hideOnLoginPage": "false", "userInfoUrl": "https://stg-id.uaepass.ae/idshub/userinfo", "validateSignature": "false", "clientId": "sandbox_stage", "uiLocales": "false", "disableNonce": "false", "useJwksUrl": "true", "pkceEnabled": "false", "authorizationUrl": "https://stg-id.uaepass.ae/idshub/authorize?acr_values=urn:safelayer:tws:policies:authentication:level:low", "disableUserInfo": "false", "logoutUrl": "https://stg-id.uaepass.ae/idshub/logout", "passMaxAge": "false"}}, {"alias": "google", "internalId": "7840c6bf-a71b-4c07-8dd1-9338290e41cf", "providerId": "google", "enabled": true, "updateProfileFirstLoginMode": "on", "trustEmail": true, "storeToken": true, "addReadTokenRoleOnCreate": false, "authenticateByDefault": false, "linkOnly": false, "firstBrokerLoginFlowAlias": "Social Login", "postBrokerLoginFlowAlias": "Social Login", "config": {"hideOnLoginPage": "false", "acceptsPromptNoneForwardFromClient": "false", "clientId": "818739705411-lbelm3fv8qa891dqkj7firieolac0ect.apps.googleusercontent.com", "disableUserInfo": "false", "filteredByClaim": "false", "syncMode": "IMPORT", "clientSecret": "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", "guiOrder": "1"}}, {"alias": "apple", "displayName": "apple", "internalId": "d7fb98f8-0e2d-4115-8307-77c01f51a0a2", "providerId": "apple", "enabled": true, "updateProfileFirstLoginMode": "on", "trustEmail": false, "storeToken": false, "addReadTokenRoleOnCreate": false, "authenticateByDefault": false, "linkOnly": false, "firstBrokerLoginFlowAlias": "first broker login", "config": {"hideOnLoginPage": "false", "acceptsPromptNoneForwardFromClient": "false", "clientId": "mg.test.iohealth.com", "displayName": "apple", "filteredByClaim": "false", "keyId": "C2NLC55H44", "p8Content": "-----BEGIN PRIVATE KEY-----\\nMIGTAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBHkwdwIBAQQgKqlm7kGU9VnPmaV5\\nD0NXtEBtMg6ZUTvttf64vvMIGhCgCgYIKoZIzj0DAQehRANCAAQPDaIX7lXEI0VW\\nXrTZBJxaZ22ivk8xtxvULFUYpp8sbFWXhpGA1PfYiQyoCxAxYKnf4+4dsBu37qy0\\nRsptYEYl\\n-----END PRIVATE KEY-----", "disableUserInfo": "false", "teamId": "M87F2FZSTA", "syncMode": "IMPORT", "clientSecret": "dummy", "guiOrder": "2"}}], "identityProviderMappers": [{"id": "66492459-7ff4-4754-afa7-4b034550d618", "name": "First Name", "identityProviderAlias": "uae_pass", "identityProviderMapper": "oidc-user-attribute-idp-mapper", "config": {"syncMode": "INHERIT", "claim": "firstnameEN", "user.attribute": "firstName"}}, {"id": "41b65b81-d0bc-410d-81ed-c528c877116a", "name": "Identity", "identityProviderAlias": "uae_pass", "identityProviderMapper": "oidc-user-attribute-idp-mapper", "config": {"syncMode": "INHERIT", "claim": "idn", "user.attribute": "national_id"}}, {"id": "a5184279-1967-4c2f-8f73-323fb8aed648", "name": "Last Name", "identityProviderAlias": "uae_pass", "identityProviderMapper": "oidc-user-attribute-idp-mapper", "config": {"syncMode": "INHERIT", "claim": "lastnameEN", "user.attribute": "lastName"}}], "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "1fce57bc-2c5e-4b79-89db-48c3f4c114a8", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "14da7c7f-af66-4e42-925e-0e84167b3b2f", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "73479889-ceb3-4f9a-8ce5-b434da52a739", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "ca3e9450-74d8-41e1-9633-9964be70b98e", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-role-list-mapper", "saml-user-property-mapper", "oidc-full-name-mapper", "oidc-usermodel-property-mapper", "oidc-usermodel-attribute-mapper", "saml-user-attribute-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-address-mapper"]}}, {"id": "1c236d6f-3ff6-4861-88f3-108cf2fdee53", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "96aba4ec-f253-4454-80f6-c0f8ce91da82", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "2ff07c55-2f8f-4a63-9da9-a7a31b7a5f8a", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "7659df50-4a76-497f-b5c5-51fdb88206a6", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-role-list-mapper", "saml-user-attribute-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-address-mapper", "oidc-usermodel-property-mapper", "oidc-full-name-mapper", "saml-user-property-mapper", "oidc-usermodel-attribute-mapper"]}}], "org.keycloak.keys.KeyProvider": [{"id": "15228f02-79f3-4f61-bf39-ba89302e5656", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"kid": ["ef5c8837-2517-466d-b631-9b55cf91b28d"], "secret": ["9WTJPJnOisr39sn92mnnVA"], "priority": ["102"]}}, {"id": "468b6af8-2071-4c0b-85c6-80edf41b58f3", "name": "rsa", "providerId": "rsa", "subComponents": {}, "config": {"privateKey": ["-----BEGIN RSA PRIVATE KEY-----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\n50nTOUm0tFHyS/3XduWLzKJQA/CHNgk=\n-----END RSA PRIVATE KEY-----\n"], "certificate": ["MIIEpzCCAo8CBgGUofAkjDANBgkqhkiG9w0BAQsFADAXMRUwEwYDVQQDDAx0ZXN0LXNlaGhhdGkwHhcNMjUwMTI2MDkyNTU3WhcNMzUwMTI2MDkyNzM3WjAXMRUwEwYDVQQDDAx0ZXN0LXNlaGhhdGkwggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoICAQDCkn6FHoPN6QK9ed7xj6j9MumvO4yMxz5pxwjN76BfkRIfS0Nhsj4dkoZLsDv4AaGgNdig7bbVqPdt17OisOlrS0ztwh+Ibmenbbcd2aD152u8AZ/ZDwlrcYHGDTx/EDnL2rauIbEl3uvs5wpp9VedL2+b6718NqvhkxnMmQWHA0TWKC8XvkGK398iy2qMpTA8NwMm3hvOCqda0SSiY6/GouBtWaIJsfSXfIBtJJAMRqfae5p5bo4VuUsa6cRsVu1JbHbsChQUyCgTCZpOv95JAo1NFMo5N+WcTAUycHJliS3on85UPWVv5lvEZRkc3lbnuLPwQDO8MMWi6qyj+V5tu5l0KyEAgYjuzvD6y8fI9AhpNThSDhbWagyHL44nL7S6++NbY4owS4QgAKIIxsa+TfwJBdcOKzVV0LLEAKPUuTn+lknuESZMehZCSWzYFvw6WSJ8XFHJPVEpVhi79j2RDgcrBnJJenifEfDthDL0Q9sJk3pTPlsoSip8KyVK2lNpPDdyziKVt8KMTO29lEqE1dDpYYkIgZCliMnfMgeV9TOD4OiVQXSEx/YqfTzko6RcA5Wv3n35+6T/1fEOEnwj7fQaooMz4HqRyCW5lk0hpz2JO2w79VJ2IyxMD1lK2rtz2Wo2yJGRh9AQzfAodEIIeWa5LeiLYDPabhWATg8MWQIDAQABMA0GCSqGSIb3DQEBCwUAA4ICAQAbxcFNafaKIn8rxhAC+GfNsc3Aay1SyGAyhG9ySfrFSNTH9u4noS5ko5s6eQy5fgcgi1mZdzKAX/5Pb8hf39r5fiDAkHyn2BV4KKa8K04OI1jyzq+DMyyQvT4N5sCCGMnhDAK+Zk8VgrwcBxlcjFRgbx89eraxNWihBuoaRQ3Y/MlZXf1THlv5K1SNDLneAq3ONiyiH3kV3MUY/HsxhLUVY9ldwkvCPGJWuOc6EQ2OlBlOMw3R8wfiKY8vOcKFWRleQ/LhJBcXMPXPi/nJDqVHFST86cZUM4db+zU5cEdj2/B6JyXGY1grwggrOw71prHDneXTNhKfYS7ngUXuwnpLcoaJqzIo1vfd2710K+KkNQWBC/ZXke7rkgexiiHhCDE0kyVmMPIHhApHFljrP7P5FjPWJn2M8CN/JJVrQ06qXbxIlVMcWVOLKaBnyRhlnYyuSPbGvYrE6uzYWKlTlc/mkY7X8GiwXdXuQhn7uyg756j10m/A9SvM4iqskgsaL+0g/xREpfXU1UsZ7Q2nQxsOP5d2COtAUpkMneNxSKCZSHPk7jWEe9Y8w1c3Dc8+RNNx0Dw14YxnxRPd+cKM4/Y2q8jZ/royNjLH3fx5re91k0XHzNofiShVq6X1SkmYuohE/xd1OG7Btej/LsQz17owBWnfnQOcodyRt7ufpqNxmg=="], "active": ["true"], "priority": ["102"], "enabled": ["true"], "algorithm": ["RS256"]}}, {"id": "e3750f5d-c6d9-4b56-8b9e-22d324f48c3c", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["0de37b27-f49c-444b-950f-ea26d3b71392"], "secret": ["k0X3UYzu7AkiIORZDFwvdCvdcFDVD64pLtnw8IR2If10LDAeCxbDlExwGeYod7hw3_-N29Tv2rbc7oqn6z3tmg"], "priority": ["102"], "algorithm": ["HS256"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "7f689dc4-e86e-4a82-99d8-6a16a1f64f56", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false}]}, {"id": "9702393c-fd59-411f-bbd8-006e6b0adb80", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "8017b96d-55fc-461d-9e0f-3e645f5a08b7", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "1fd752b6-bc60-456b-8deb-7f93bcd9b651", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "877190d7-3c52-46f9-980f-69978ba46a7f", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "Account verification options", "userSetupAllowed": false}]}, {"id": "ef59c7ed-79be-4dc3-93cd-e5ea118c2d53", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "e4bd78be-c2d7-40dc-9333-68338df98a2b", "alias": "Social Login", "description": "", "providerId": "basic-flow", "topLevel": true, "builtIn": false, "authenticationExecutions": [{"authenticator": "social-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 0, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "acb553ea-9781-44f8-9ae1-5cbd0e8b3994", "alias": "UAE Login", "description": "", "providerId": "basic-flow", "topLevel": true, "builtIn": false, "authenticationExecutions": [{"authenticator": "auth-cookie", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 0, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 1, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "identity-provider-redirector", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 2, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "custom-auth-username-password-form", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 3, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "3af6a966-f8d7-45cf-b0eb-f2d46cdce511", "alias": "UAE Pass", "description": "", "providerId": "basic-flow", "topLevel": true, "builtIn": false, "authenticationExecutions": [{"authenticator": "uae-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 0, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "a5fb7e19-452a-4d96-8ed4-4450ce6949bd", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false}]}, {"id": "99028dca-2765-41e7-bf45-f91b9d226810", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false}]}, {"id": "c54c2314-52d1-4634-8b9d-0078cec8ee19", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "identity-provider-redirector", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 25, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": true, "flowAlias": "forms", "userSetupAllowed": false}]}, {"id": "7b1d5789-7ad2-44f4-8b8b-b1de417d0deb", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-secret-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-x509", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 40, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "1c78fdcf-1dc3-4932-88ab-96f4859a71e5", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 30, "autheticatorFlow": true, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false}]}, {"id": "4b56f0da-dac2-45db-8965-38e828c2425a", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "da0f620f-8fea-4151-a269-b09edef4df23", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "User creation or linking", "userSetupAllowed": false}]}, {"id": "9f463a2a-8b7b-4220-868b-188a5af231e8", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false}]}, {"id": "cf43924a-7ea6-4092-8f35-3d055b76b0a6", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "authenticatorFlow": true, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": true, "flowAlias": "registration form", "userSetupAllowed": false}]}, {"id": "c5bf58c9-9ccc-461b-b119-6d5f885d001e", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-password-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 50, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-recaptcha-action", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 60, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "b66cecf3-4ae8-4f5a-8ac2-ccd1875b0f9c", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-credential-email", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 40, "autheticatorFlow": true, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false}]}, {"id": "6d415ada-5b55-4dce-a3a2-6b1951dda788", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}], "authenticatorConfig": [{"id": "f1c721d8-c45f-4cbf-8e79-2942218cff84", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "5c91508f-4d1f-40c0-a4cd-64ea88508626", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "TERMS_AND_CONDITIONS", "name": "Terms and Conditions", "providerId": "TERMS_AND_CONDITIONS", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "delete_account", "name": "Delete Account", "providerId": "delete_account", "enabled": false, "defaultAction": false, "priority": 60, "config": {}}, {"alias": "webauthn-register-passwordless", "name": "Webauthn Register Passwordless", "providerId": "webauthn-register-passwordless", "enabled": false, "defaultAction": false, "priority": 70, "config": {}}, {"alias": "webauthn-register", "name": "Webauthn Register", "providerId": "webauthn-register", "enabled": true, "defaultAction": false, "priority": 80, "config": {}}, {"alias": "update_user_locale", "name": "Update User Locale", "providerId": "update_user_locale", "enabled": true, "defaultAction": false, "priority": 1000, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {"cibaBackchannelTokenDeliveryMode": "poll", "cibaAuthRequestedUserHint": "login_hint", "clientOfflineSessionMaxLifespan": "0", "oauth2DevicePollingInterval": "5", "clientSessionIdleTimeout": "86400", "actionTokenGeneratedByUserLifespan.idp-verify-account-via-email": "", "actionTokenGeneratedByUserLifespan.verify-email": "", "clientOfflineSessionIdleTimeout": "0", "actionTokenGeneratedByUserLifespan.execute-actions": "", "cibaInterval": "5", "realmReusableOtpCode": "false", "cibaExpiresIn": "120", "oauth2DeviceCodeLifespan": "600", "parRequestUriLifespan": "60", "clientSessionMaxLifespan": "86400", "shortVerificationUri": "", "actionTokenGeneratedByUserLifespan.reset-credentials": ""}, "keycloakVersion": "23.0.7", "userManagedAccessAllowed": false, "clientProfiles": {"profiles": []}, "clientPolicies": {"policies": []}}