USE
[test_user_management];
GO

-- View 1: health_programs_doctors_view
CREATE
OR ALTER
VIEW [dbo].[health_programs_doctors_view]
AS

SELECT
    [doctor].[id] AS [doctor_id], [prog].[name] AS [program_name], [prog].[id] AS [program_id], [net].[name] AS [network_name], [net].[id] AS [network_id]

FROM
    [test_health_program].[dbo].[hlth_prgrm] AS [prog]
    JOIN [test_health_program].[dbo].[rel_hlth_prgrm_networks] AS [prog_net]
ON [prog_net].[health_program_id] = [prog].[id]
    JOIN [test_health_program].[dbo].[hlth_prgrm_ntwrk] AS [net]
    ON [prog_net].[health_program_networks_id] = [net].[id]
    JOIN [test_health_program].[dbo].[hlth_prgrm_ntwrk_prvdr] AS [ntwrk_prvdr]
    ON [ntwrk_prvdr].[health_program_network_id] = [net].[id]
    JOIN [doctor_doctor] AS [doctor]
    ON [doctor].[vendor_id] = [ntwrk_prvdr].[provider_id];
GO

-- View 2: user_network_view
CREATE
OR ALTER
VIEW [dbo].[user_network_view]
AS

SELECT
    [p].[provider_id] AS [provider_id], [m].[user_id] AS [user_id]

FROM
    [test_health_program].[dbo].[hlth_prgrm_ntwrk_prvdr] AS [p]
    JOIN [test_health_program].[dbo].[hlth_prgrm_ntwrk] AS [n]
ON [n].[id] = [p].[health_program_network_id]
    JOIN [test_health_program].[dbo].[hlth_prgrm_mmbr] AS [m]
    ON [n].[name] = [m].[network]
    AND [n].[payer_id] = [m].[insurance_company_id]
    AND [n].[category_code] = [m].[category_code]
    AND [p].[is_active] = 1
    AND [n].[policy_number] = [m].[insurance_policy_number]

WHERE [m].[membership_start] <= GETDATE()
  AND [m].[membership_end] >= GETDATE()
  AND [n].[is_active] = 1;
GO

-- View 3: vendor_health_packages_view
CREATE
OR ALTER
VIEW [dbo].[vendor_health_packages_view]
AS

SELECT hp.[vendor_id] AS [vendor_id],
    hp.[id] AS [health_package_id]

FROM
    [dbo].[vendor_vendor] AS v
    JOIN [test_marketplace].[dbo].[health_package] AS hp
ON v.[id] = hp.[vendor_id]

WHERE
    hp.[is_published] = 1;
GO