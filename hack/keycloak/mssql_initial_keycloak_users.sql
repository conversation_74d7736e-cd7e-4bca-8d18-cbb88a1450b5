-- Make sure the table is empty and the id generator is set to 0
use demo_user_management;

-- Make sure sso_id is the same as the keycloak users IDs, for the two users respectively.

-- User 1 (<EMAIL>)
INSERT INTO [demo_user_management].[dbo].[account_user] (
    [last_login], [deleted], [deleted_at], [email], [first_name], [last_name],
    [is_active], [note], [date_joined], [avatar], [mobile], [mobile_verified],
    [email_verified], [sso_id], [default_billing_address_id], [default_shipping_address_id],
    [deleted_by_id], [vendor_id], [app_role], [app_type], [total_money_spent],
    [total_orders_count], [national_id], [last_password_reset_date], [meeting_platform_id],
    [payer_id], [date_of_birth], [gender], [photo], [second_name], [third_name],
    [vendor_user_type], [vendor_id_null_to_zero], [parent_user_id], [full_name],
    [preferred_language_id], [relation_type], [created], [created_by_id], [modified],
    [modified_by_id], [terms_and_conditions_accepted_version], [default_branch_id],
    [delete_reason], [two_factor_auth_enabled], [two_factor_auth_secret], [two_factor_auth_verification_method], [ask_to_enable_bio_login]
)
VALUES (
    NULL, 0, NULL, '<EMAIL>', 'Integration', 'Integration',
    1, NULL, '2024-12-01 05:54:59.713884', NULL, NULL, 0,
    0, 'dccfe24f-f7f2-40ca-a77f-12858bceae5d', NULL, NULL,
    NULL, NULL, 'Admin', 'Admin', 0.00,
    0.00, NULL, NULL, NULL,
    NULL, NULL, NULL, '', NULL, NULL,
    NULL, 0, NULL, 'Integration Integration',
    NULL, NULL, '2024-12-01 05:54:59.714356', NULL, '2024-12-01 05:54:59.714391',
    NULL, 0, NULL,
    NULL, 0, NULL, NULL, 0
    );

-- User 2 (<EMAIL>)
INSERT INTO [demo_user_management].[dbo].[account_user] (
    [last_login], [deleted], [deleted_at], [email], [first_name], [last_name],
    [is_active], [note], [date_joined], [avatar], [mobile], [mobile_verified],
    [email_verified], [sso_id], [default_billing_address_id], [default_shipping_address_id],
    [deleted_by_id], [vendor_id], [app_role], [app_type], [total_money_spent],
    [total_orders_count], [national_id], [last_password_reset_date], [meeting_platform_id],
    [payer_id], [date_of_birth], [gender], [photo], [second_name], [third_name],
    [vendor_user_type], [vendor_id_null_to_zero], [parent_user_id], [full_name],
    [preferred_language_id], [relation_type], [created], [created_by_id], [modified],
    [modified_by_id], [terms_and_conditions_accepted_version], [default_branch_id],
    [delete_reason], [two_factor_auth_enabled], [two_factor_auth_secret], [two_factor_auth_verification_method], [ask_to_enable_bio_login]
)
VALUES (
    NULL, 0, NULL, '<EMAIL>', 'Super', 'Admin',
    1, NULL, '2024-12-01 07:50:18.106430', NULL, '123456789', 0,
    0, '278a8afc-2ffd-4100-9852-8350a720546c', NULL, NULL,
    NULL, NULL, 'Admin', 'Admin', 0.00,
    0.00, '123456789', NULL, NULL,
    NULL, NULL, NULL, '', NULL, NULL,
    NULL, 0, NULL, 'Super Admin',
    NULL, NULL, '2024-12-01 07:50:18.106738', NULL, '2024-12-01 07:50:18.106766',
    NULL, 0, NULL,
    NULL, 0, NULL, NULL, 0
    );