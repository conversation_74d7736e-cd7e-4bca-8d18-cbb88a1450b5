#!/bin/bash
set -e

curl --fail -H "Authorization: Bearer Oracle" -L0 http://***************/opc/v2/instance/metadata/oke_init_script | base64 --decode >/var/run/oke-init.sh
bash /var/run/oke-init.sh

WAZUH_MANAGER_IP='*************'
TAG_NAMESPACE='wazuh_config'

# --- 1. Fetch Metadata from OCI ---
INSTANCE_HOSTNAME=$(curl -s -H "Authorization: Bearer Oracle" -L http://***************/opc/v2/instance/hostname)
UNIQUE_SUFFIX=$(echo $INSTANCE_HOSTNAME | tail -c 9)
CLUSTER_GROUP=$(curl -s -H "Authorization: Bearer Oracle" -L http://***************/opc/v2/instance/definedTags | jq -r ".\"${TAG_NAMESPACE}\".cluster_group")

if [ -z "$CLUSTER_GROUP" ]; then
    CLUSTER_GROUP="oke-unassigned"
fi

# --- 2. Construct the Wazuh Agent Name ---
# This will result in a name like "io-test-worker-xyz"
FINAL_AGENT_NAME="${CLUSTER_GROUP}-${UNIQUE_SUFFIX}"

echo "Configuring Wazuh Agent with name: ${FINAL_AGENT_NAME}"

# --- 3. Install and Configure Wazuh Agent ---
curl -o /tmp/wazuh-agent.rpm https://packages.wazuh.com/4.x/yum/wazuh-agent-4.12.0-1.x86_64.rpm
sudo WAZUH_MANAGER="${WAZUH_MANAGER_IP}" WAZUH_AGENT_NAME="${FINAL_AGENT_NAME}" dnf -y localinstall /tmp/wazuh-agent.rpm

# --- 4. Enable and Start the Service ---
sudo systemctl daemon-reload
sudo systemctl enable wazuh-agent
sudo systemctl start wazuh-agent

echo "Wazuh Agent installation and configuration complete."

# --- 5. Oracle Linux Package Upgrade and Conditional Reboot ---
echo "Starting Oracle Linux package upgrade..."

# Update package cache
sudo dnf makecache

# Check if there are any available updates
UPDATES_AVAILABLE=$(sudo dnf check-update --quiet; echo $?)

if [ $UPDATES_AVAILABLE -eq 100 ]; then
    echo "Updates are available. Proceeding with package upgrade..."

    # Store current kernel version before upgrade
    CURRENT_KERNEL=$(uname -r)
    echo "Current kernel version: $CURRENT_KERNEL"

    # Perform the upgrade
    sudo dnf -y update

    # Check if kernel was updated (Oracle Linux uses UEK - Unbreakable Enterprise Kernel)
    # Get the latest installed kernel of the correct type
    LATEST_KERNEL=$(rpm -q kernel-uek --last 2>/dev/null | head -n1 | awk '{print $1}' | sed "s/kernel-uek-//")

    echo "Latest installed $KERNEL_PACKAGE: $LATEST_KERNEL"

    # Compare kernel versions to determine if reboot is needed
    if [ "$CURRENT_KERNEL" != "$LATEST_KERNEL" ]; then
        echo "$KERNEL_PACKAGE was upgraded from $CURRENT_KERNEL to $LATEST_KERNEL"
        echo "System reboot is required. Scheduling reboot in 1 minute..."

        # Log the reboot reason
        logger "Oracle Linux $KERNEL_PACKAGE upgrade completed. Rebooting from $CURRENT_KERNEL to $LATEST_KERNEL"

        # Schedule reboot in 1 minute to allow services to finish initialization
        sudo shutdown -r +1 "System reboot required after kernel upgrade"

        echo "Reboot scheduled. System will restart in 1 minute."
    else
        echo "No kernel upgrade detected. Reboot not required."

        # Check if any services need restart after package updates
        if command -v needs-restarting >/dev/null 2>&1; then
            echo "Checking for services that need restart..."
            SERVICES_TO_RESTART=$(sudo needs-restarting -s 2>/dev/null || true)
            if [ -n "$SERVICES_TO_RESTART" ]; then
                echo "The following services should be restarted:"
                echo "$SERVICES_TO_RESTART"
                # Optionally restart services automatically
                echo "$SERVICES_TO_RESTART" | xargs -r sudo systemctl restart
            fi
        fi
    fi

    echo "Package upgrade completed successfully."
else
    echo "No updates available. System is up to date."
fi

echo "Oracle Linux package upgrade process completed."

