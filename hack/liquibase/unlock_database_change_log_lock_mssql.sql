-- Simple script to unlock all Liquibase locks and display lock status

-- Find all schemas with DATABASEC<PERSON>NGELOGLOCK tables
SELECT
    CONCAT('UPDATE ', table_schema, '.DATABASECHANGELOGLOCK SET LOCKED = 0, LOCKGRANTED = NULL, LOCKEDBY = NULL;') AS 'Execute_This_Command'
FROM
    information_schema.tables
WHERE
    table_name = 'DATABASECHANGELOGLOCK'
  AND table_schema LIKE 'prd\_%'
ORDER BY
    table_schema;

-- After executing the above commands, run this to check lock status (0 = unlocked, 1 = locked)
SELECT
    CONCAT('SELECT "', table_schema, '" AS schema_name, LOCKED FROM ', table_schema, '.DATABASECHANGELOGLOCK;') AS 'Check_Lock_Status_Command'
FROM
    information_schema.tables
WHERE
    table_name = 'DATABASECHANGELOGLOCK'
  AND (
    table_schema LIKE 'prd\_%'
    )
ORDER BY
    table_schema;