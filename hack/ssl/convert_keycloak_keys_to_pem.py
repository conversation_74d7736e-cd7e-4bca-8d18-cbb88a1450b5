import base64
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.backends import default_backend
from cryptography.exceptions import InvalidSignature
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.asymmetric import padding

def convert_to_pem(key_str, key_type):
    """Convert single-line key to PEM format with proper headers"""
    if "PRIVATE" in key_type:
        # Keycloak's RSA private keys are typically PKCS#1 format
        header = "-----BEGIN RSA PRIVATE KEY-----\n"
        footer = "\n-----END RSA PRIVATE KEY-----"
    else:
        header = "-----B<PERSON>IN PUBLIC KEY-----\n"
        footer = "\n-----END PUBLIC KEY-----"

    # Split into 64-character lines
    key_lines = '\n'.join([key_str[i:i+64] for i in range(0, len(key_str), 64)])

    return header + key_lines + footer

def validate_key_pair(public_pem, private_pem):
    """Verify if the public and private keys match"""
    try:
        # Load public key
        pub_key = serialization.load_pem_public_key(
            public_pem.encode(),
            backend=default_backend()
        )

        # Load private key (using PKCS#1 format)
        priv_key = serialization.load_pem_private_key(
            private_pem.encode(),
            password=None,
            backend=default_backend()
        )

        # Create test message
        message = b"Keycloak key validation test"

        # Sign with private key
        signature = priv_key.sign(
            message,
            padding.PKCS1v15(),
            hashes.SHA256()
        )

        # Verify with public key
        pub_key.verify(
            signature,
            message,
            padding.PKCS1v15(),
            hashes.SHA256()
        )

        return True
    except Exception as e:
        print(f"Validation error: {str(e)}")
        return False

# Example usage
if __name__ == "__main__":
    # Replace with your actual keys
    raw_public_key = "MIIBIjANBgkqhkiG9w0BAQE..."  # From Realm Settings -> Keys -> Public Key (rsa-generated type)
    raw_private_key = "MIIEowIBAAKCAQEAz7eXKnQ..."  # From COMPONENT_CONFIG table (privateKey field with name rsa-generated)

    # Convert to PEM format
    public_pem = convert_to_pem(raw_public_key, "PUBLIC")
    private_pem = convert_to_pem(raw_private_key, "PRIVATE")

    print("Public Key PEM:")
    print(public_pem)
    print("\nPrivate Key PEM:")
    print(private_pem)

    # Validate the key pair
    if validate_key_pair(public_pem, private_pem):
        print("\n✅ Keys are valid and match")
    else:
        print("\n❌ Key mismatch or invalid format")