apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: "marketplace-application-set"
spec:
  goTemplate: true
  goTemplateOptions: [ "missingkey=error" ]

  generators:
    - list:
        elements:
            # IO Applications 
          - path: "overlays/io/test/marketplace"
            clusterIP: "https://**************:6443"
          - path: "overlays/io/demo/marketplace"
            clusterIP: "https://*************:6443"
          - path: "overlays/io/stg/marketplace"
            clusterIP: "https://**************:6443"
          - path: "overlays/io/prd/marketplace"
            clusterIP: "https://**************:6443"

            # Liva Applications 
#          - path: "overlays/liva/stg/marketplace"
#            clusterIP: "https://*************:6443"
#          - path: "overlays/liva/prd/marketplace"
#            clusterIP: "https://**************:6443"
#
#            # Willis Applications
#          - path: "overlays/willis/stg/marketplace"
#            clusterIP: "https://**************:6443"
#          - path: "overlays/willis/prd/marketplace"
#            clusterIP: "https://**************:6443"

            # SGH Applications
          - path: "overlays/sgh/prd/marketplace"
            clusterIP: "https://**************:6443"

            # Med Gulf Applications
          - path: "overlays/med-gulf/test/marketplace"
            clusterIP: "https://***********:6443"

#          - path: "overlays/med-gulf/demo/marketplace"
#            clusterIP: "https://************:6443"

        template:
          spec:
            project: '{{ index (splitList "/" .path) 1 }}'
            destination: {}
          metadata: {}

  template:
    metadata:
      name: 'marketplace-{{ index (splitList "/" .path) 1  }}-{{ index (splitList "/" .path) 2  }}'
    spec:
      project: "default" # placeholder, project value will be overriden from spec.generators[0].template.spec.project

      source:
        path: "{{ .path }}"
        repoURL: "**************:my-workforce/Kubernetes.git"
        targetRevision: "iohealth-main"

      destination:
        namespace: '{{ index (splitList "/" .path) 2 }}'
        server: '{{ .clusterIP }}'

      syncPolicy:
        syncOptions:
          - CreateNamespace=true
        automated:
          prune: true
          selfHeal: true