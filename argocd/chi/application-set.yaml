apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: "chi-application-set"
spec:
  goTemplate: true
  goTemplateOptions: [ "missingkey=error" ]

  generators:
    - list:
        elements:
            # IO Applications 
          - path: "overlays/io/test/chi"
            clusterIP: "https://**************:6443"
          - path: "overlays/io/demo/chi"
            clusterIP: "https://*************:6443"
          - path: "overlays/io/stg/chi"
            clusterIP: "https://**************:6443"
          - path: "overlays/io/prd/chi"
            clusterIP: "https://**************:6443"

            # Liva Applications 
#          - path: "overlays/liva/stg/chi"
#            clusterIP: "https://*************:6443"
#          - path: "overlays/liva/prd/chi"
#            clusterIP: "https://**************:6443"
#
#            # Willis Applications
#          - path: "overlays/willis/stg/chi"
#            clusterIP: "https://**************:6443"
#          - path: "overlays/willis/prd/chi"
#            clusterIP: "https://**************:6443"

            # SGH Applications
          - path: "overlays/sgh/prd/chi"
            clusterIP: "https://**************:6443"

            # Med Gulf Applications
#          - path: "overlays/med-gulf/test/chi"
#            clusterIP: "https://***********:6443"

#          - path: "overlays/med-gulf/demo/chi"
#            clusterIP: "https://************:6443"

        template:
          spec:
            project: '{{ index (splitList "/" .path) 1 }}'
            destination: {}
          metadata: {}

  template:
    metadata:
      name: 'chi-{{ index (splitList "/" .path) 1  }}-{{ index (splitList "/" .path) 2  }}'
    spec:
      project: "default" # placeholder, project value will be overriden from spec.generators[0].template.spec.project

      source:
        path: "{{ .path }}"
        repoURL: "**************:my-workforce/Kubernetes.git"
        targetRevision: "iohealth-main"

      destination:
        namespace: '{{ index (splitList "/" .path) 2 }}'
        server: '{{ .clusterIP }}'

      syncPolicy:
        syncOptions:
          - CreateNamespace=true
        automated:
          prune: true
          selfHeal: true