apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: "ingress-nginx-application-set"
spec:
  goTemplate: true
  goTemplateOptions: [ "missingkey=error" ]

  generators:
    - list:
        elements:
            # IO Applications 
          - path: "overlays/io/test/ingress-nginx"
            clusterIP: "https://**************:6443"
          - path: "overlays/io/demo/ingress-nginx"
            clusterIP: "https://*************:6443"
          - path: "overlays/io/stg/ingress-nginx"
            clusterIP: "https://**************:6443"
          - path: "overlays/io/prd/ingress-nginx"
            clusterIP: "https://**************:6443"

            # Liva Applications 
#          - path: "overlays/liva/stg/ingress-nginx"
#            clusterIP: "https://*************:6443"
#          - path: "overlays/liva/prd/ingress-nginx"
#            clusterIP: "https://**************:6443"
#
#            # Willis Applications
#          - path: "overlays/willis/stg/ingress-nginx"
#            clusterIP: "https://**************:6443"
#          - path: "overlays/willis/prd/ingress-nginx"
#            clusterIP: "https://**************:6443"

            # SGH Applications
          - path: "overlays/sgh/prd/ingress-nginx"
            clusterIP: "https://**************:6443"

            # Med Gulf Applications
          - path: "overlays/med-gulf/test/ingress-nginx"
            clusterIP: "https://***********:6443"

#          - path: "overlays/med-gulf/demo/ingress-nginx"
#            clusterIP: "https://************:6443"

            # Digital Twin Applications
          - path: "overlays/digital-twin/prd/ingress-nginx"
            clusterIP: "https://************:6443"

        template:
          spec:
            project: '{{ index (splitList "/" .path) 1 }}'
            destination: {}
          metadata: {}

  template:
    metadata:
      name: 'ingress-nginx-{{ index (splitList "/" .path) 1  }}-{{ index (splitList "/" .path) 2  }}'
    spec:
      project: "default" # placeholder, project value will be overriden from spec.generators[0].template.spec.project

      source:
        path: "{{ .path }}"
        repoURL: "**************:my-workforce/Kubernetes.git"
        targetRevision: "iohealth-main"

      destination:
        namespace: ingress-nginx
        server: '{{ .clusterIP }}'

      syncPolicy:
        syncOptions:
          - CreateNamespace=true
        automated:
          prune: true
          selfHeal: true