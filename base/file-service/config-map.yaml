apiVersion: v1
kind: ConfigMap
metadata:
  name: file-configmap
data:
  DB_HOST: "$(DB_HOST)"
  ENV: "$(ENV)"
  OCI_REGION: "$(OCI_S3_REGION)"
  SPRING_SECURITY_OAUTH2_CLIENT_PROVIDER_OIDC_ISSUER_URI: "https://api.$(ROOT_DOMAIN)/auth/realms/$(KEYCLOAK_REALM)"
  OCI_ENDPOINT: "$(OCI_S3_ENDPOINT)"
  OCI_BUCKET_NAME: "$(FILE_SERVICE_S3_BUCKET)"
  OCI_BULK_BUCKET_NAME: "$(FILE_SERVICE_S3_BUCKET)"
  JHIPSTER_CORS_ALLOWED_ORIGINS: "https://gcadmin.$(ROOT_DOMAIN),https://admin.$(ROOT_DOMAIN),https://client.$(ROOT_DOMAIN),https://provider.$(ROOT_DOMAIN),https://optima.$(ROOT_DOMAIN),https://consumer.$(ROOT_DOMAIN)"
  SPRING_SERVLET_MULTIPART_MAX_FILE_SIZE: "50MB"
  SPRING_SERVLET_MULTIPART_MAX_REQUEST_SIZE: "50MB"
  APOLLO_GRAPHQL_CLIENT_API_URL: "http://gateway/graphql"