apiVersion: v1
kind: ConfigMap
metadata:
  name: "temporal-config"
  labels:
    app.kubernetes.io/name: temporal
    helm.sh/chart: temporal-0.51.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/instance: temporal
    app.kubernetes.io/version: "1.25.2"
    app.kubernetes.io/part-of: temporal
data:
  config_template.yaml: |-
    log:
      stdout: true
      level: "debug,info"

    persistence:
      defaultStore: default
      visibilityStore: visibility
      numHistoryShards: 512
      datastores:
        default:
          sql:
            pluginName: "mysql8"
            driverName: "mysql8"
            databaseName: "$(ENV)_temporal"
            connectAddr: "$(TEMPORAL_DB_HOST):3306"
            connectProtocol: "tcp"
            user: "$(TEMPORAL_DB_USER)"
            password: {{ .Env.TEMPORAL_STORE_PASSWORD | quote }}
            maxConnLifetime: 1h
            maxConns: 20
            secretName: ""
        visibility:
          sql:
            pluginName: "mysql8"
            driverName: "mysql8"
            databaseName: "$(ENV)_temporal_visibility"
            connectAddr: "$(TEMPORAL_DB_HOST):3306"
            connectProtocol: "tcp"
            user: "$(TEMPORAL_DB_USER)"
            password: {{ .Env.TEMPORAL_VISIBILITY_STORE_PASSWORD  | quote }}
            maxConnLifetime: 1h
            maxConns: 20
            secretName: ""

    global:
      membership:
        name: temporal
        maxJoinDuration: 30s
        broadcastAddress: {{ default .Env.POD_IP "0.0.0.0" }}

      pprof:
        port: 7936

      metrics:
        tags:
          type: {{ .Env.SERVICES }}
        prometheus:
          timerType: histogram
          listenAddress: "0.0.0.0:9090"

    services:
      frontend:
        rpc:
          grpcPort: 7233
          httpPort: 7243
          membershipPort: 6933
          bindOnIP: "0.0.0.0"

      history:
        rpc:
          grpcPort: 7234
          membershipPort: 6934
          bindOnIP: "0.0.0.0"

      matching:
        rpc:
          grpcPort: 7235
          membershipPort: 6935
          bindOnIP: "0.0.0.0"

      worker:
        rpc:
          membershipPort: 6939
          bindOnIP: "0.0.0.0"

    clusterMetadata:
      enableGlobalDomain: false
      failoverVersionIncrement: 10
      masterClusterName: "active"
      currentClusterName: "active"
      clusterInformation:
        active:
          enabled: true
          initialFailoverVersion: 1
          rpcName: "temporal-frontend"
          rpcAddress: "127.0.0.1:7233"
          httpAddress: "127.0.0.1:7243"

    dcRedirectionPolicy:
      policy: "noop"
      toDC: ""

    archival:
      history:
        enableRead: true
        provider:
          filestore:
            dirMode: "0766"
            fileMode: "0666"
        state: enabled
      visibility:
        enableRead: true
        provider:
          filestore:
            dirMode: "0766"
            fileMode: "0666"
        state: enabled
    namespaceDefaults:
      archival:
        history:
          URI: file:///tmp/temporal_archival/development
          state: enabled
        visibility:
          URI: file:///tmp/temporal_vis_archival/development
          state: enabled

    publicClient:
      hostPort: "temporal-frontend:7233"

    dynamicConfigClient:
      filepath: "/etc/temporal/dynamic_config/dynamic_config.yaml"
      pollInterval: "10s"
