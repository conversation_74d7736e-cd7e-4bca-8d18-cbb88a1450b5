apiVersion: v1
kind: Service
metadata:
  name: temporal-admintools
  labels:
    app.kubernetes.io/component: admintools
    app.kubernetes.io/name: temporal
    helm.sh/chart: temporal-0.51.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/instance: temporal
    app.kubernetes.io/version: "1.25.2"
    app.kubernetes.io/part-of: temporal
spec:
  type: ClusterIP 
  ports:
    - port: 22
      targetPort: 22
      protocol: TCP
      name: ssh

  selector:
    app.kubernetes.io/name: temporal
    app.kubernetes.io/instance: temporal
    app.kubernetes.io/component: admintools
