apiVersion: v1
kind: ConfigMap
metadata:
  name: "temporal-job-config"
  labels:
    app.kubernetes.io/name: temporal
    helm.sh/chart: temporal-0.51.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/instance: temporal
    app.kubernetes.io/version: "1.25.2"
    app.kubernetes.io/part-of: temporal
data:
  SQL_PLUGIN: "mysql8"
  SQL_HOST: "$(TEMPORAL_DB_HOST)"
  SQL_PORT: "3306"
  SQL_USER: "$(TEMPORAL_DB_USER)"
  TEMPORAL_SQL_DATABASE: "$(ENV)_temporal"
  TEMPORAL_VIS_SQL_DATABASE: "$(ENV)_temporal_visibility"