---
# Source: temporal/templates/server-secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: temporal-default-store
  labels:
    app.kubernetes.io/name: temporal
    helm.sh/chart: temporal-0.51.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/instance: temporal
    app.kubernetes.io/version: "1.25.2"
    app.kubernetes.io/part-of: temporal
type: Opaque
data:
  password: "$(DB_PASS)"
---
# Source: temporal/templates/server-secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: temporal-visibility-store
  labels:
    app.kubernetes.io/name: temporal
    helm.sh/chart: temporal-0.51.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/instance: temporal
    app.kubernetes.io/version: "1.25.2"
    app.kubernetes.io/part-of: temporal
type: Opaque
data:
  password: "$(DB_PASS)"
