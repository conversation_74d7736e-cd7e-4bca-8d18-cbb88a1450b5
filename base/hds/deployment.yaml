apiVersion: apps/v1
kind: Deployment
metadata:
  name: hds
spec:
  replicas: 1
  selector:
    matchLabels:
      app: hds
  template:
    metadata:
      annotations:
        reloader.stakater.com/auto: "true"
      labels:
        app: hds
    spec:
      containers:
        - image: hds
          name: hds
          ports:
            - containerPort: 8000
              name: hds
          envFrom:
            - configMapRef:
                name: hds-configmap
            - secretRef:
                name: hds-secret
          command:
            - /bin/sh
            - -c
            - "PYTHONPATH=$PWD uvicorn --app-dir=hds app:create_app --factory --host 0.0.0.0 --port 8000 --workers 6"
      imagePullSecrets:
        - name: ocirsecret