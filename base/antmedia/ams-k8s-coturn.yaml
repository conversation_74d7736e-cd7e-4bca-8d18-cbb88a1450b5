apiVersion: v1
kind: Service
metadata:
  name: coturn
  labels:
       app.kubernetes.io/name: coturn
       app.kubernetes.io/instance: coturn
       app.kubernetes.io/version: 0.0.1
  annotations:
    oci.oraclecloud.com/load-balancer-type: "nlb"
spec:
  type: LoadBalancer
  externalTrafficPolicy: Local
  ports:
    - port: 3478
      targetPort: 3478
      protocol: UDP
      name: turn-port1
    - port: 3478
      targetPort: 3478
      protocol: TCP
      name: turn-port2
    
  selector:
       app.kubernetes.io/name: coturn
       app.kubernetes.io/instance: coturn
       app.kubernetes.io/version: 0.0.1
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: coturn
  labels:
    app.kubernetes.io/name: coturn
    app.kubernetes.io/instance: coturn
    app.kubernetes.io/version: 0.0.1
spec:
  # replicas: 1
  selector:
    matchLabels:
          app.kubernetes.io/name: coturn
          app.kubernetes.io/instance: coturn
          app.kubernetes.io/version: 0.0.1
  template:
    metadata:
      labels:
            app.kubernetes.io/name: coturn
            app.kubernetes.io/instance: coturn
            app.kubernetes.io/version: 0.0.1
    spec:
      containers:
        - name: coturn
          image: coturn
          imagePullPolicy: Always
          env:
          - name: MY_POD_IP
            valueFrom:
              fieldRef:
                fieldPath: status.podIP
          ports:
            - name: turn-port1
              containerPort: 3478
              hostPort: 3478
              protocol: UDP
            - name: turn-port2
              containerPort: 3478
              hostPort: 3478
              protocol: TCP
          args:
            - "-a"
#            - "-f"
            - "--user=admin:admin"
            - "-p"
            - "3478"
            - "-v"
            - "--external-ip=************/$MY_POD_IP"
            - "--realm=admin"
            - "--listening-ip=0.0.0.0"
            - "--relay-ip=$MY_POD_IP"
            - "--min-port=49152"
            - "--max-port=65535"
      imagePullSecrets:
        - name: ocirsecret
