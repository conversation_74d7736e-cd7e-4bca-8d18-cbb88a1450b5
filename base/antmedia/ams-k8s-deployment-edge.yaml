---
# Source: antmedia/templates/ams-k8s-deployment-edge.yaml
kind: Service
apiVersion: v1
metadata:
  name: ant-media-server-edge
spec:
  selector:
    app: ant-media-edge
  ports:
    - name: http
      protocol: TCP
      port: 5080
---
# Source: antmedia/templates/ams-k8s-deployment-edge.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ant-media-server-edge
spec:
  selector:
    matchLabels:
      app: ant-media-edge
  replicas: 1
  template:
    metadata:
      labels:
        app: ant-media-edge
    spec:
      dnsPolicy: ClusterFirstWithHostNet
      initContainers:
        - name: init-antmedia
          image: ant-media-server
          command:
            - sh
            - -c
            - |
              if [ ! -f /usr/local/antmedia/start.sh ]; then
                echo "Initializing persistent volume with default AMS files..."
                cp -r /antmedia_defaults/* /usr/local/antmedia/
              else
                chown -R antmedia:antmedia /usr/local/antmedia
                chmod +x /usr/local/antmedia/start.sh
                echo "Persistent volume already initialized."
              fi
          # It is important that the init container has the same volume mount as the main container.
          volumeMounts:
            - name: antmedia-edge-config
              mountPath: /usr/local/antmedia
      containers:
      - name: ant-media-server
        image: ant-media-server
        imagePullPolicy: IfNotPresent
        args: ["-g", "false", "-s", "false", "-r", "true", "-m", "cluster", "-h", "mongo", "-l", "AMSb1f125b41373ce3e9f9bc840f35683", "-k", "kafka-cluster-kafka-bootstrap:9092"]
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /
            port: 5080
            scheme: HTTP
          initialDelaySeconds: 15
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /
            port: 5080
            scheme: HTTP
          initialDelaySeconds: 5
          periodSeconds: 5
          successThreshold: 1
          timeoutSeconds: 1
        volumeMounts:
        - mountPath: /tmp
          name: temp-volume
        - mountPath: /usr/local/antmedia
          name: antmedia-edge-config
      imagePullSecrets:
        - name: ocirsecret
      volumes:
      - hostPath:
          path: /temp-data
          type: DirectoryOrCreate
        name: temp-volume
      - name: antmedia-edge-config
        persistentVolumeClaim:
          claimName: antmedia-edge-pvc
