apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ant-media-server-origin
  annotations:
    nginx.ingress.kubernetes.io/affinity: cookie
#    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/healthcheck-interval: 5s
    nginx.ingress.kubernetes.io/healthcheck-path: /
    nginx.ingress.kubernetes.io/healthcheck-port: "5080"
    nginx.ingress.kubernetes.io/healthcheck-timeout: 3s
    nginx.ingress.kubernetes.io/proxy-body-size: 0m
    nginx.ingress.kubernetes.io/proxy-next-upstream: error timeout http_502
    nginx.ingress.kubernetes.io/proxy-next-upstream-tries: "3"
    nginx.ingress.kubernetes.io/session-cookie-expires: "172800"
    nginx.ingress.kubernetes.io/session-cookie-max-age: "172800"
    nginx.ingress.kubernetes.io/session-cookie-name: route
    nginx.ingress.kubernetes.io/ssl-protocols: TLSv1.2 TLSv1.3
  
spec:
  ingressClassName: nginx
  
  rules:
  - host: "am-origin.$(ROOT_DOMAIN)"
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ant-media-server-origin
            port:
              number: 5080
