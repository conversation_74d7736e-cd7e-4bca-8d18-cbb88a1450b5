FROM ubuntu:22.04

ARG LicenseKey
ARG BranchName=master

RUN apt-get update && apt-get install -y curl wget iproute2 cron logrotate dnsutils iptables

RUN cd home \
    && pwd \
    && wget https://raw.githubusercontent.com/ant-media/Scripts/${BranchName}/install_ant-media-server.sh \
    && chmod 755 install_ant-media-server.sh

RUN cd /home \
    && pwd \
    && if [ -n "$LicenseKey" ]; then \
           ./install_ant-media-server.sh -l ${LicenseKey} -s false; \
       else \
           echo "LicenseKey argument is not provided. Aborting the build process."; \
           exit 1; \
       fi

# Install media push plugin (Very Important) for conference calls !!
RUN echo "test"; \
    echo "#!/bin/bash\n\$@" > /usr/bin/sudo; \
    chmod +x /usr/bin/sudo; \
    wget -O install_media-push-plugin.sh https://raw.githubusercontent.com/ant-media/Plugins/master/MediaPushPlugin/src/main/script/install_media-push-plugin.sh; \
    bash ./install_media-push-plugin.sh;


# Copy the installed AMS to a backup folder that will not be affected by PVC mounts (for K8s init container)
RUN cp -r /usr/local/antmedia /antmedia_defaults

ENTRYPOINT ["/usr/local/antmedia/start.sh"]
