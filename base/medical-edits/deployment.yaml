apiVersion: apps/v1
kind: Deployment
metadata:
  name: medical-edits
spec:
  selector:
    matchLabels:
      app: medical-edits
  template:
    metadata:
      annotations:
        reloader.stakater.com/auto: "true"
      labels:
        app: medical-edits
    spec:
      containers:
        - image: medical-edits
          name: medical-edits
          resources:
            requests:
              cpu: 200m
              memory: 1.5Gi
            limits:
              memory: 1.5Gi
          readinessProbe:
            httpGet:
              path: /management/health/readiness
              port: 5060
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          livenessProbe:
            httpGet:
              path: /management/health/liveness
              port: 5060
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          envFrom:
            - configMapRef:
                name: db-type
            - configMapRef:
                name: medical-edits-configmap
            - secretRef:
                name: medical-edits-secret
          ports:
            - containerPort: 5060
              name: medical-edits
      imagePullSecrets:
        - name: ocirsecret