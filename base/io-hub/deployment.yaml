apiVersion: apps/v1
kind: Deployment
metadata:
  name: io-hub
spec:
  replicas: 1
  selector:
    matchLabels:
      app: io-hub
  template:
    metadata:
      annotations:
        reloader.stakater.com/auto: "true"
      labels:
        app: io-hub
    spec:
      containers:
        - image: io-hub
          name: io-hub
          readinessProbe:
            httpGet:
              path: /management/health/readiness
              port: 8040
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          livenessProbe:
            httpGet:
              path: /management/health/liveness
              port: 8040
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          envFrom:
            - configMapRef:
                name: db-type
            - configMapRef:
                name: io-hub-configmap
            - secretRef:
                name: io-hub-secret
          ports:
            - containerPort: 8040
      imagePullSecrets:
        - name: ocirsecret