---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: openvpn
  labels:
    app: openvpn
    chart: openvpn-4.2.5
    release: openvpn
    heritage: Helm
spec:
  replicas: 1
  selector:
    matchLabels:
      app: openvpn
      release: openvpn
  template:
    metadata:
      labels:
        app: openvpn
        release: openvpn
      annotations:
        checksum/config: 7443098a74b7a4d4967a460a1a4aa0c560f2b0c6fd732cdd18247d7a382e3e17
    spec:
      containers:
      - name: openvpn
        image: openvpn-img
        imagePullPolicy: IfNotPresent
        command: ["/etc/openvpn/setup/configure.sh"]
        ports:
        - containerPort: 443
          name: openvpn
        securityContext:
          privileged: true
          capabilities:
            add:
              - NET_ADMIN
        readinessProbe:
          initialDelaySeconds: 5
          periodSeconds: 5
          successThreshold: 2
          exec:
            command:
            - nc
            - -z
            - 127.0.0.1
            - "443"
        resources:
          requests:
            cpu: "350m"
            memory: "175Mi"
          limits:
            cpu: "400m"
            memory: "200Mi"
        volumeMounts:
          - mountPath: /etc/openvpn/setup
            name: openvpn
            readOnly: false
          - mountPath: /etc/openvpn/certs
            name: certs
            readOnly: false
      volumes:
      - name: openvpn
        configMap:
          name: openvpn
          defaultMode: 0775
      - name: certs
        persistentVolumeClaim:
          claimName: openvpn

      imagePullSecrets:
        - name: ocirsecret
