apiVersion: apps/v1
kind: Deployment
metadata:
  name: integration-api
spec:
  selector:
    matchLabels:
      app: integration-api
  template:
    metadata:
      annotations:
        reloader.stakater.com/auto: "true"
      labels:
        app: integration-api
    spec:
      containers:
        - image: integration-api
          name: integration-api
          resources:
            requests:
              cpu: 300m
              memory: 1.5Gi
            limits:
              memory: 1.5Gi
          readinessProbe:
            httpGet:
              path: /management/health/readiness
              port: 8078
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          livenessProbe:
            httpGet:
              path: /management/health/liveness
              port: 8078
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          envFrom:
            - configMapRef:
                name: db-type
            - configMapRef:
                name: integration-api-configmap
            - secretRef:
                name: integration-api-secret
          ports:
            - containerPort: 8078
              name: integration-api
      imagePullSecrets:
        - name: ocirsecret