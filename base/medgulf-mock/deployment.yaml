apiVersion: apps/v1
kind: Deployment
metadata:
  name: medgulf-mock
spec:
  selector:
    matchLabels:
      app: medgulf-mock
  template:
    metadata:
      annotations:
        reloader.stakater.com/auto: "true"
      labels:
        app: medgulf-mock
    spec:
      containers:
        - image: medgulf-mock
          name: medgulf-mock
          readinessProbe:
            httpGet:
              path: /management/health/readiness
              port: 3050
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          livenessProbe:
            httpGet:
              path: /management/health/liveness
              port: 3050
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
#          envFrom:
#            - configMapRef:
#                name: db-type
#            - configMapRef:
#                name: medgulf-mock-configmap
#            - secretRef:
#                name: medgulf-mock-secret
          ports:
            - containerPort: 3050
              name: medgulf-mock
      imagePullSecrets:
        - name: ocirsecret