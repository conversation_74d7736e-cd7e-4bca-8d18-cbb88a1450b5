apiVersion: apps/v1
kind: Deployment
metadata:
  name: flowise-integration
spec:
  selector:
    matchLabels:
      app: flowise-integration
  template:
    metadata:
      annotations:
        reloader.stakater.com/auto: "true"
      labels:
        app: flowise-integration
    spec:
      containers:
        - image: flowise-integration
          name: flowise-integration
          resources:
            requests:
              cpu: 200m
              memory: 1.5Gi
            limits:
              cpu: "1"
              memory: 1.5Gi
          readinessProbe:
            httpGet:
              path: /management/health/readiness
              port: 3060
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          livenessProbe:
            httpGet:
              path: /management/health/liveness
              port: 3060
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          envFrom:
            - configMapRef:
                name: db-type
            - configMapRef:
                name: flowise-integration-configmap
            - secretRef:
                name: flowise-integration-secret
          ports:
            - containerPort: 3060
      imagePullSecrets:
        - name: ocirsecret