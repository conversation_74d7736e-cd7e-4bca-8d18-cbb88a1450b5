apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-management
spec:
  selector:
    matchLabels:
      app: user-management
  template:
    metadata:
      annotations:
        reloader.stakater.com/auto: "true"
      labels:
        app: user-management
    spec:
      containers:
        - image: user-management
          name: user-management
          resources:
            requests:
              cpu: 500m
              memory: 4Gi
            limits:
              memory: 4Gi
          readinessProbe:
            httpGet:
              path: /health/
              port: 8000
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          livenessProbe:
            httpGet:
              path: /health/
              port: 8000
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          ports:
            - containerPort: 8000
              name: user-management
          volumeMounts:
            - name: keycloak-private-key-volume
              mountPath: app/keys/sehatuk.pem
              subPath: sehatuk.pem
          envFrom:
            - configMapRef:
                name: db-type
            - configMapRef:
                name: user-management-configmap
            - secretRef:
                name: user-management-secret
      imagePullSecrets:
        - name: ocirsecret
      volumes:
        - name: keycloak-private-key-volume
          configMap:
            name:
              user-management-configmap