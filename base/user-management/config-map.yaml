apiVersion: v1
kind: ConfigMap
metadata:
  name: user-management-configmap
data:
  DB_HOST: "$(DB_HOST)"
  ENV: "$(ENV)"
  WORKFLOW_API_HOST: "http://process-order-flow:8080"
  GOOGLE_CHAT_SPACE_URL: "$(USER_MGMT_GOOGLE_SPACE_URL)"
  DEFAULT_FROM_EMAIL: "<EMAIL>"
  KEYCLOAK_SERVER_URL: "https://api.$(ROOT_DOMAIN)"
  KEYCLOAK_INTERNAL_SERVER_URL: "http://keycloak:9080"
  KEYCLOAK_REALM: "$(KE<PERSON><PERSON><PERSON>K_REALM)"
  KEYCLOAK_CLIENT_ID: "gateway_web_app"
  KEYCLOAK_ADMIN_CLIENT_ID: 'admin-cli'
  LOAD_CUSTOM_SETTINGS: "True"
  FEDERATED_BACKEND_URL: "http://gateway/graphql"
  TOKEN_SIG_PRIVATE_KEY_PATH: "/app/keys/sehatuk.pem"
  ENABLE_ACCOUNT_CONFIRMATION_BY_SMS: "True"
  KAFKA_BOOTSTRAP_SERVERS: "kafka-cluster-kafka-bootstrap:9092"
  GOOGLE_CHAT_LOG_ENABLED: "True"
  REDIS_URL: "redis://redis:6379"
  PASSWORD_SET_LINK: "https://client.$(ROOT_DOMAIN)/set-password"
  KEYCLOAK_INTEGRATION_CLIENT_ID: "integration_client"
  HIDE_IS_EXPIRED_EXCEPTION: "True"
  HIDE_TOKEN_GOOGLE: "True"
  DEBUG: "True"
  DB_DEBUG: "False"
  ALLOWED_CLIENT_HOSTS: "*"
  SYNC_KEYCLOAK: "False"
  USE_JSON_LOGGING: "False"
  OTP_CODE_EXPIRATION_TIME: "300"
  OPEN_SEARCH_HOST: "$(OPEN_SEARCH_HOST_NAME)"
  OPEN_SEARCH_PORT: "$(OPEN_SEARCH_PORT)"
  OPEN_SEARCH_USER_NAME: "$(OPEN_SEARCH_USERNAME)"
  OPEN_SEARCH_USE_SSL: "True"

  # Feature Flags, can be customized in the specific environment kustomization file, for example (io-stg env): overlays/io-stg/user-management/kustomization.yaml
  CUSTOMER_REGISTRATION_FEATURE_ENABLED: "$(FEATURE_FLAG_CUSTOMER_REGISTRATION_FEATURE_ENABLED)"
  DELETE_CUSTOMER_ACCOUNT_FEATURE_ENABLED: "$(FEATURE_FLAG_DELETE_CUSTOMER_ACCOUNT_FEATURE_ENABLED)"
  ADD_NATIONAL_ID_FEATURE_ENABLED: "$(FEATURE_FLAG_ADD_NATIONAL_ID_FEATURE_ENABLED)"
  DEPENDENT_CREATION_FEATURE_ENABLED: "$(FEATURE_FLAG_DEPENDENT_CREATION_FEATURE_ENABLED)"
  FILTER_DOCTORS_BY_PATIENT_NETWORK: "$(FEATURE_FLAG_FILTER_DOCTORS_BY_PATIENT_NETWORK)"
  TWO_FACTOR_AUTHENTICATION_FEATURE_ENABLED: "$(FEATURE_FLAG_TWO_FACTOR_AUTHENTICATION_FEATURE_ENABLED)"
  BIOMETRIC_LOGIN_FEATURE_ENABLED: "True"

  PROJECT_NAME: "$(PROJECT_NAME)"
  sehatuk.pem: "$(KEYCLOAK_RSA_PRIVATE_KEY)"
