apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-sada
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/configuration-snippet: |
      set_real_ip_from 10.0.0.0/16;
      if ($http_x_forwarded_proto = http) {
        return 301 https://$server_name$request_uri;
      }
      add_header Access-Control-Allow-Origin *;
      proxy_cache_bypass $http_upgrade;

spec:
  rules:
    - host: "sada.$(ROOT_DOMAIN)"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: sada-frontend
                port:
                  number: 80