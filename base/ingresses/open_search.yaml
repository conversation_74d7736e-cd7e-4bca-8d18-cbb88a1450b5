apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-open-search-route
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTPS"
spec:
  rules:
    - host: "api.$(ROOT_DOMAIN)"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: opensearch-cluster-master
                port:
                  number: 9200