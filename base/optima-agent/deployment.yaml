apiVersion: apps/v1
kind: Deployment
metadata:
  name: optima-agent
spec:
  replicas: 8
  selector:
    matchLabels:
      app: optima-agent
  template:
    metadata:
      annotations:
        reloader.stakater.com/auto: "true"
      labels:
        app: optima-agent
    spec:
      containers:
        - image: optima-agent
          name: optima-agent
          readinessProbe:
            httpGet:
              path: /management/health/readiness
              port: 5090
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          livenessProbe:
            httpGet:
              path: /management/health/liveness
              port: 5090
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          envFrom:
            - configMapRef:
                name: db-type
            - configMapRef:
                name: optima-agent-configmap
            - secretRef:
                name: optima-agent-secret
          ports:
            - containerPort: 5090
      imagePullSecrets:
        - name: ocirsecret