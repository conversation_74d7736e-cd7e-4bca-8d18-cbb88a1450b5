apiVersion: v1
kind: ConfigMap
metadata:
  name: optima-agent-configmap
data:
  DB_HOST: "$(DB_HOST)"
  ENV: "$(ENV)"
  SPRING_SECURITY_OAUTH2_CLIENT_PROVIDER_OIDC_ISSUER_URI: "https://api.$(ROOT_DOMAIN)/auth/realms/$(KEYCLOAK_REALM)"
  APOLLO_GRAPHQL_CLIENT_API_URL: "http://gateway/graphql"
  SPRING_KAFKA_BOOTSTRAP_SERVERS: "kafka-cluster-kafka-bootstrap:9092"
  SPRING_KAFKA_PRODUCER_BOOTSTRAP_SERVERS: "kafka-cluster-kafka-bootstrap:9092"
  JHIPSTER_CORS_ALLOWED_ORIGINS: "https://optima.$(ROOT_DOMAIN),https://gcadmin.$(ROOT_DOMAIN),https://admin.$(ROOT_DOMAIN),https://client.$(ROOT_DOMAIN),https://provider.$(ROOT_DOMAIN)"
  PROVIDER_URL: "https://api.$(ROOT_DOMAIN)/optima_mock/api/check-request"
  FILE_SERVICE_URL: "http://file:8079/api"
  # TODO: Needs to be checked
  KEYCLOAK_INTEGRATION_SECRET_KEY: "$(KEYCLOAK_INTEGRATION_CLIENT_SECRET)"
  OPTIMA_RCM_ENABLE: "$(OPTIMA_RCM_ENABLE)"
  OPTIMA_AGENT_QUEUE_MEDIUM_MAX_IN_PROGRESS: "20"