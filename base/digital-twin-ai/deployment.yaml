apiVersion: apps/v1
kind: Deployment
metadata:
  name: digital-twin-ai
spec:
  selector:
    matchLabels:
      app: digital-twin-ai
  template:
    metadata:
      annotations:
        reloader.stakater.com/auto: "true"
      labels:
        app: digital-twin-ai
    spec:
      containers:
        - image: digital-twin-ai
          name: digital-twin-ai
          resources:
            requests:
              cpu: 200m
              memory: 1.5Gi
            limits:
              cpu: "1"
              memory: 1.5Gi
          readinessProbe:
            httpGet:
              path: /management/health/readiness
              port: 5010
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          livenessProbe:
            httpGet:
              path: /management/health/liveness
              port: 5010
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          envFrom:
            - configMapRef:
                name: db-type
            - configMapRef:
                name: digital-twin-ai-configmap
            - secretRef:
                name: digital-twin-ai-secret
          ports:
            - containerPort: 5010
              name: digital-twin-ai
      imagePullSecrets:
        - name: ocirsecret