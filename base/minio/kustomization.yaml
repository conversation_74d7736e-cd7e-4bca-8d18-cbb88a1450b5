apiVersion: kustomize.config.k8s.io/v1beta1

kind: Kustomization

namespace: minio

commonAnnotations:
  operator.min.io/authors: "MinIO, Inc."
  operator.min.io/license: "AGPLv3"
  operator.min.io/support: "https://subnet.min.io"
  operator.min.io/version: v7.0.0

commonLabels:
  app.kubernetes.io/name: minio-operator

resources:
  - namespace.yaml
  - service-account.yaml
  - cluster-role.yaml
  - cluster-role-binding.yaml
  - crds/
  - service.yaml
  - deployment.yaml

  # Deploy MinIO Tenant (The Actual MinIO Instance)
  - tenant/