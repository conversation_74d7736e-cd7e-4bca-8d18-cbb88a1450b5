apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-companion
spec:
  selector:
    matchLabels:
      app: ai-companion
  template:
    metadata:
      annotations:
        reloader.stakater.com/auto: "true"
      labels:
        app: ai-companion
    spec:
      containers:
        - image: ai-companion
          name: ai-companion
          resources:
            requests:
              cpu: "100m"
              memory: "1.5Gi"
            limits:
              memory: "1.5Gi"
          readinessProbe:
            httpGet:
              path: /management/health/readiness
              port: 8000
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          livenessProbe:
            httpGet:
              path: /management/health/liveness
              port: 8000
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          envFrom:
            - configMapRef:
                name: db-type
            - configMapRef:
                name: ai-companion-configmap
            - secretRef:
                name: ai-companion-secret
          ports:
            - containerPort: 8000
      imagePullSecrets:
        - name: ocirsecret