apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-companion-configmap
data:
  DB_HOST: "$(DB_HOST)"
  ENV: "$(ENV)"
  APOLLO_GRAPHQL_CLIENT_API_URL: "http://gateway/graphql"
  APOLLO_GRAPHQL_CLIENT_TOKEN_INTEGRATION_CLIENT_SECRET: "$(KEYCLOAK_INTEGRATION_CLIENT_SECRET)"
  SPRING_SECURITY_OAUTH2_CLIENT_PROVIDER_OIDC_ISSUER_URI: "https://api.$(ROOT_DOMAIN)/auth/realms/$(KEYCLOAK_REALM)"
  LIBRETRANSLATE_URL: "http://libretranslate.$(ENV).svc.cluster.local:5000/translate"