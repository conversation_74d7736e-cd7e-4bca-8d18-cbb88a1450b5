apiVersion: v1
kind: ConfigMap
metadata:
  name: io-companion-configmap
data:

  GRAPHQL_API_URL: "http://gateway/graphql"
  KEYCLOAK_AUTH_URL: "https://api.$(ROOT_DOMAIN)/auth/realms/$(<PERSON><PERSON><PERSON><PERSON><PERSON>K_REALM)/api/login"

  # Database
  POSTGRES_DSN: "********************************************************/iocompanion"
  MEMORY_BACKEND: "postgres"
  MEMORY_DB: "memory.db"

  # Redis
  REDIS_HOST: "redis://redis:6379"

  # LLM Configuration
  MODEL_NAME: "Qwen/Qwen2.5-72B-Instruct"
  MODEL_HOST: "https://pale-harriott-iohealth-87394596.koyeb.app/v1"
  MODEL_API_KEY: "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"
  MODEL_TEMPERATURE: "0.3"
  MODEL_TOP_P: "0.95"

  # Timezones
  DEFAULT_TIMEZONE: "Asia/Dubai"

  # API Configuration
  ENABLE_STREAMING: "false"
  LOG_LEVEL: "DEBUG"
  LOG_FILE: "logs/app.log"
  LOG_MAX_BYTES: "5242880"
  LOG_BACKUP_COUNT: "5"
  LOG_TO_CONSOLE: "true"
  MAX_SEARCH_DAYS: "14"
  CATEGORY_CACHE_TTL: "168"
  SPECIALTY_CACHE_TTL: "168"
  CONTEXT_CACHE_TTL_HOURS: "168"