apiVersion: v1
kind: ConfigMap
metadata:
  name: mysql-init-scripts
  labels:
    app.kubernetes.io/instance: mysql
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: mysql
    app.kubernetes.io/version: 8.4.3
    helm.sh/chart: mysql-12.2.1
    app.kubernetes.io/part-of: mysql
    app.kubernetes.io/component: primary
data:
  init.sql: |
    CREATE DATABASE IF NOT EXISTS $(ENV)_temporal;
    CREATE DATABASE IF NOT EXISTS $(ENV)_temporal_visibility;
