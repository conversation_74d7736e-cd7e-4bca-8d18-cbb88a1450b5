apiVersion: v1
kind: Service
metadata:
  name: mysql-metrics
  labels:
    app.kubernetes.io/instance: mysql
    app.kubernetes.io/managed-by: <PERSON><PERSON>
    app.kubernetes.io/name: mysql
    app.kubernetes.io/version: 8.4.3
    helm.sh/chart: mysql-12.2.1
    app.kubernetes.io/part-of: mysql
    app.kubernetes.io/component: metrics
  annotations:
    prometheus.io/port: "9104"
    prometheus.io/scrape: "true"
spec:
  type: ClusterIP
  ports:
    - port: 9104
      targetPort: metrics
      protocol: TCP
      name: metrics
  selector:
    app.kubernetes.io/instance: mysql
    app.kubernetes.io/name: mysql
