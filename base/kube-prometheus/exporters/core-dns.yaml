---
# Source: kube-prometheus-stack/templates/exporters/core-dns/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: kube-prometheus-stack-coredns
  labels:
    app: kube-prometheus-stack-coredns
    jobLabel: coredns
    
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/instance: kube-prometheus-stack
    app.kubernetes.io/version: "66.2.1"
    app.kubernetes.io/part-of: kube-prometheus-stack
    chart: kube-prometheus-stack-66.2.1
    release: "kube-prometheus-stack"
    heritage: "Helm"
  namespace: kube-system
spec:
  clusterIP: None
  ports:
    - name: http-metrics
      port: 9153
      protocol: TCP
      targetPort: 9153
  selector:
    k8s-app: kube-dns
---
# Source: kube-prometheus-stack/templates/exporters/core-dns/servicemonitor.yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: kube-prometheus-stack-coredns
  labels:
    app: kube-prometheus-stack-coredns
    
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/instance: kube-prometheus-stack
    app.kubernetes.io/version: "66.2.1"
    app.kubernetes.io/part-of: kube-prometheus-stack
    chart: kube-prometheus-stack-66.2.1
    release: "kube-prometheus-stack"
    heritage: "Helm"
spec:
  jobLabel: jobLabel
  
  selector:
    matchLabels:
      app: kube-prometheus-stack-coredns
      release: "kube-prometheus-stack"
  namespaceSelector:
    matchNames:
      - "kube-system"
  endpoints:
  - port: http-metrics
    bearerTokenFile: /var/run/secrets/kubernetes.io/serviceaccount/token
