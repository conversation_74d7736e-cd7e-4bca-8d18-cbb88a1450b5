---
# Source: kube-prometheus-stack/templates/prometheus/clusterrole.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: kube-prometheus-stack-prometheus
  labels:
    app: kube-prometheus-stack-prometheus
    
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/instance: kube-prometheus-stack
    app.kubernetes.io/version: "66.2.1"
    app.kubernetes.io/part-of: kube-prometheus-stack
    chart: kube-prometheus-stack-66.2.1
    release: "kube-prometheus-stack"
    heritage: "Helm"
rules:
# This permission are not in the kube-prometheus repo
# they're grabbed from https://github.com/prometheus/prometheus/blob/master/documentation/examples/rbac-setup.yml
- apiGroups: [""]
  resources:
  - nodes
  - nodes/metrics
  - services
  - endpoints
  - pods
  verbs: ["get", "list", "watch"]
- apiGroups: ["discovery.k8s.io"]
  resources:
  - endpointslices
  verbs: ["get", "list", "watch"]
- apiGroups:
  - "networking.k8s.io"
  resources:
  - ingresses
  verbs: ["get", "list", "watch"]
- nonResourceURLs: ["/metrics", "/metrics/cadvisor"]
  verbs: ["get"]
---
# Source: kube-prometheus-stack/templates/prometheus/clusterrolebinding.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: kube-prometheus-stack-prometheus
  labels:
    app: kube-prometheus-stack-prometheus
    
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/instance: kube-prometheus-stack
    app.kubernetes.io/version: "66.2.1"
    app.kubernetes.io/part-of: kube-prometheus-stack
    chart: kube-prometheus-stack-66.2.1
    release: "kube-prometheus-stack"
    heritage: "Helm"
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: kube-prometheus-stack-prometheus
subjects:
  - kind: ServiceAccount
    name: kube-prometheus-stack-prometheus

---
# Source: kube-prometheus-stack/templates/prometheus/prometheus.yaml
apiVersion: monitoring.coreos.com/v1
kind: Prometheus
metadata:
  name: kube-prometheus-stack-prometheus
  labels:
    app: kube-prometheus-stack-prometheus
    
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/instance: kube-prometheus-stack
    app.kubernetes.io/version: "66.2.1"
    app.kubernetes.io/part-of: kube-prometheus-stack
    chart: kube-prometheus-stack-66.2.1
    release: "kube-prometheus-stack"
    heritage: "Helm"
spec:
  automountServiceAccountToken: true
  alerting:
    alertmanagers:
#      - namespace: monitoring
      - name: kube-prometheus-stack-alertmanager
        port: http-web
        pathPrefix: "/"
        apiVersion: v2
  image: "quay.io/prometheus/prometheus:v2.55.1"
  version: v2.55.1
  externalUrl: http://kube-prometheus-stack-prometheus.monitoring:9090
  paused: false
  replicas: 1
  shards: 1
  logLevel:  info
  logFormat:  logfmt
  listenLocal: false
  enableAdminAPI: false
  retention: "10d"
  tsdb:
    outOfOrderTimeWindow: 0s
  walCompression: true
  routePrefix: "/"
  serviceAccountName: kube-prometheus-stack-prometheus
  serviceMonitorSelector:
    matchLabels:
      release: "kube-prometheus-stack"

  serviceMonitorNamespaceSelector: {}
  podMonitorSelector:
    matchLabels:
      release: "kube-prometheus-stack"

  podMonitorNamespaceSelector: {}
  probeSelector:
    matchLabels:
      release: "kube-prometheus-stack"

  probeNamespaceSelector: {}
  securityContext:
    fsGroup: 2000
    runAsGroup: 2000
    runAsNonRoot: true
    runAsUser: 1000
    seccompProfile:
      type: RuntimeDefault
  ruleNamespaceSelector: {}
  ruleSelector:
    matchLabels:
      release: "kube-prometheus-stack"

  scrapeConfigSelector:
    matchLabels:
      release: "kube-prometheus-stack"

  scrapeConfigNamespaceSelector: {}
  affinity:
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          topologyKey: kubernetes.io/hostname
          labelSelector:
            matchExpressions:
              - {key: app.kubernetes.io/name, operator: In, values: [prometheus]}
              - {key: prometheus, operator: In, values: [kube-prometheus-stack-prometheus]}
  portName: http-web
  hostNetwork: false
---
# Source: kube-prometheus-stack/templates/prometheus/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: kube-prometheus-stack-prometheus
  labels:
    app: kube-prometheus-stack-prometheus
    self-monitor: "true"
    
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/instance: kube-prometheus-stack
    app.kubernetes.io/version: "66.2.1"
    app.kubernetes.io/part-of: kube-prometheus-stack
    chart: kube-prometheus-stack-66.2.1
    release: "kube-prometheus-stack"
    heritage: "Helm"
spec:
  ports:
  - name: http-web
    port: 9090
    targetPort: 9090
  - name: reloader-web
    appProtocol: http
    port: 8080
    targetPort: reloader-web
  publishNotReadyAddresses: false
  selector:
    app.kubernetes.io/name: prometheus
    operator.prometheus.io/name: kube-prometheus-stack-prometheus
  sessionAffinity: None
  type: "ClusterIP"
---
# Source: kube-prometheus-stack/templates/prometheus/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: kube-prometheus-stack-prometheus
  labels:
    app: kube-prometheus-stack-prometheus
    app.kubernetes.io/name: kube-prometheus-stack-prometheus
    app.kubernetes.io/component: prometheus
    
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/instance: kube-prometheus-stack
    app.kubernetes.io/version: "66.2.1"
    app.kubernetes.io/part-of: kube-prometheus-stack
    chart: kube-prometheus-stack-66.2.1
    release: "kube-prometheus-stack"
    heritage: "Helm"
automountServiceAccountToken: true
---
# Source: kube-prometheus-stack/templates/prometheus/servicemonitor.yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: kube-prometheus-stack-prometheus
  labels:
    app: kube-prometheus-stack-prometheus
    
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/instance: kube-prometheus-stack
    app.kubernetes.io/version: "66.2.1"
    app.kubernetes.io/part-of: kube-prometheus-stack
    chart: kube-prometheus-stack-66.2.1
    release: "kube-prometheus-stack"
    heritage: "Helm"
spec:
  
  selector:
    matchLabels:
      app: kube-prometheus-stack-prometheus
      release: "kube-prometheus-stack"
      self-monitor: "true"
  namespaceSelector:
    matchNames:
      - "monitoring"
  endpoints:
  - port: http-web
    path: "/metrics"
  - port: reloader-web
    path: "/metrics"
