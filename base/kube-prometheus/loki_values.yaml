USER-SUPPLIED VALUES:
backend:
  replicas: 0
bloomCompactor:
  replicas: 0
bloomGateway:
  replicas: 0
compactor:
  replicas: 0
deploymentMode: SingleBinary
distributor:
  replicas: 0
indexGateway:
  replicas: 0
ingester:
  replicas: 0
loki:
  auth_enabled: false
  commonConfig:
    replication_factor: 3
  limits_config:
    allow_structured_metadata: true
    retention_period: 672h # 28 days retention
    volume_enabled: true
  pattern_ingester:
    enabled: true
  schemaConfig:
    configs:
    - from: "2024-04-01"
      index:
        period: 24h
        prefix: loki_index_
      object_store: s3
      schema: v13
      store: tsdb
  storage:
    bucketNames:
      admin: loki-chunk
      chunks: loki-chunk
      ruler: loki-chunk
    s3:
      accessKeyId: 5ce479a15d65f8465d4df9e91c0acb02aaee3bec
      endpoint: https://axpebais8u12.compat.objectstorage.me-dubai-1.oraclecloud.com
#      region: me-dubai-1
      region: me-dubai-1
      s3ForcePathStyle: true
      signatureVersion: null
      insecure: false
      secretAccessKey: "7aInFV1kKDxLFXU/qcJ7NDkrpISFbEPNpFJLtHedXac="
    type: s3
#  storage_config:
#    aws:
#      bucketnames: loki-chunk
#      region: me-dubai-1
#      s3forcepathstyle: true
minio:
  enabled: false
querier:
  replicas: 0
queryFrontend:
  replicas: 0
queryScheduler:
  replicas: 0
read:
  replicas: 0
singleBinary:
  persistence:
    accessModes:
    - ReadWriteOnce
    size: 50Gi
  replicas: 3
write:
  replicas: 0
