apiVersion: v1
kind: ConfigMap
metadata:
  name: marketplace-configmap
data:
  DB_HOST: "$(DB_HOST)"
  ENV: "$(ENV)"
  SPRING_SECURITY_OAUTH2_CLIENT_PROVIDER_OIDC_ISSUER_URI: "https://api.$(ROOT_DOMAIN)/auth/realms/$(KEYCLOAK_REALM)"
  marketplace_KEYCLOAK_CLIENT_ID: "integration_client"
  OPEN_SEARCH_HOST_NAME: "$(OPEN_SEARCH_HOST_NAME)"
  OPEN_SEARCH_USERNAME: "$(OPEN_SEARCH_USERNAME)"
  OPEN_SEARCH_HOST_SCHEMA: "$(OPEN_SEARCH_HOST_SCHEMA)"
  OPEN_SEARCH_PORT: "$(OPEN_SEARCH_PORT)"
  SPRING_KAFKA_BOOTSTRAP_SERVERS: "kafka-cluster-kafka-bootstrap:9092"
  TZ: "Asia/Dubai"

  # Feature Flags, can be customized in the specific environment kustomization file, for example (io-stg env): overlays/io-stg/marketplace/kustomization.yaml
  FEATURE_FLAG_MARKETPLACE_FEATURE_ENABLED: "$(FEATURE_FLAG_MARKETPLACE_FEATURE_ENABLED)"
  FEATURE_FLAG_HEALTH_PACKAGE_FEATURE_ENABLED: "$(FEATURE_FLAG_HEALTH_PACKAGE_FEATURE_ENABLED)"
  FEATURE_FLAG_DELETE_CUSTOMER_ACCOUNT_FEATURE_ENABLED: "$(FEATURE_FLAG_DELETE_CUSTOMER_ACCOUNT_FEATURE_ENABLED)"
  FEATURE_FLAG_KNOWLEDGE_HUB_FEATURE_ENABLED: "$(FEATURE_FLAG_KNOWLEDGE_HUB_FEATURE_ENABLED)"
  FEATURE_FLAG_IN_PERSON_CHECKIN_FEATURE_ENABLED: "$(FEATURE_FLAG_IN_PERSON_CHECKIN_FEATURE_ENABLED)"
  FEATURE_FLAG_SSO_FEATURE_ENABLED: "$(FEATURE_FLAG_SSO_FEATURE_ENABLED)"
  FEATURE_FLAG_UAEPASS_FEATURE_ENABLED: "$(FEATURE_FLAG_UAEPASS_FEATURE_ENABLED)"
  FEATURE_FLAG_ADD_MEMBER_CARD_FEATURE_ENABLED: "$(FEATURE_FLAG_ADD_MEMBER_CARD_FEATURE_ENABLED)"
  PRODUCT_FEED_IGNORE_AVAILABILITY: "$(FEATURE_FLAG_PRODUCT_FEED_IGNORE_AVAILABILITY)"
  FEATURE_FLAG_DIGITAL_TWIN_FEATURE_ENABLED: "$(FEATURE_FLAG_DIGITAL_TWIN_FEATURE_ENABLED)"
  FEATURE_FLAG_MY_HEALTH_FEATURE_ENABLED: "$(FEATURE_FLAG_MY_HEALTH_FEATURE_ENABLED)"

  VENDOR_INTEGRATION_URL: "http://vendor-integration-api:7020"
  PRODUCT_FEED_CRON: "0 0/1 * * * ?"
  APOLLO_GRAPHQL_CLIENT_API_URL: "http://gateway/graphql"
  GOOGLE_SPACE_URL: "$(MARKETPLACE_GOOGLE_SPACE_URL)"

  # TODO: set this once loki is deployed with kube-prometheus-stack
  # LOKI_URL: "http://193.123.77.29/loki/api/v1/push"