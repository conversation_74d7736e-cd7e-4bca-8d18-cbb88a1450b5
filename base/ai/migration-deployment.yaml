apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-db-migrate
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-db-migrate
  template:
    metadata:
      annotations:
        reloader.stakater.com/auto: "true"
      labels:
        app: ai-db-migrate
    spec:
      containers:
        - name: ai
          image: ai
          envFrom:
            - configMapRef:
                name: ai-configmap
            - secretRef:
                name: ai-secret
          env:
            - name: USE_JSON_LOGGING
              value: "False"
            - name: LOAD_CUSTOM_SETTINGS
              value: "False"
          command:
            - /bin/sh
            - -c
            - "python3 manage.py migrate;sleep infinity & wait;"
      imagePullSecrets:
        - name: ocirsecret