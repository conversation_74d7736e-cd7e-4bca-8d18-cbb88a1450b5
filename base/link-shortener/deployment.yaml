apiVersion: apps/v1
kind: Deployment
metadata:
  name: link-shortener
spec:
  replicas: 1
  selector:
    matchLabels:
      app: link-shortener
  template:
    metadata:
      annotations:
        reloader.stakater.com/auto: "true"
      labels:
        app: link-shortener
    spec:
      containers:
        - image: link-shortener
          name: link-shortener
          readinessProbe:
            httpGet:
              path: /management/health/readiness
              port: 80
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          livenessProbe:
            httpGet:
              path: /management/health/liveness
              port: 80
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          envFrom:
            - configMapRef:
                name: db-type
            - configMapRef:
                name: link-shortener-configmap
            - secretRef:
                name: link-shortener-secret
          ports:
            - containerPort: 80
              name: link-shortener
      imagePullSecrets:
        - name: ocirsecret