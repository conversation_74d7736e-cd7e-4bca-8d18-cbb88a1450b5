# Self-signed root certificate issuer
apiVersion: cert-manager.io/v1
kind: Issuer
metadata:
  name: opensearch-root-ca-issuer
  labels:
    app: opensearch
    component: certificates
spec:
  selfSigned: {}
---
# Root Certificate Authority (CA)
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: opensearch-root-ca
  labels:
    app: opensearch
    component: certificates
spec:
  isCA: true
  commonName: opensearch-root-ca
  subject:
    organizations:
      - IOHealth
    organizationalUnits:
      - DevOps
  secretName: opensearch-root-ca-secret
  privateKey:
    algorithm: RSA
    encoding: PKCS8
  issuerRef:
    name: opensearch-root-ca-issuer
    kind: Issuer
    group: cert-manager.io
---
# Issuer for server and admin certificates
apiVersion: cert-manager.io/v1
kind: Issuer
metadata:
  name: opensearch-cert-issuer
  labels:
    app: opensearch
    component: certificates
spec:
  ca:
    secretName: opensearch-root-ca-secret
---
# Server certificate for OpenSearch nodes (node-to-node and client communication)
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: opensearch-node-cert
  labels:
    app: opensearch
    component: certificates
spec:
  secretName: opensearch-node-cert-secret
  isCA: false
  commonName: opensearch-cluster-master
  usages:
    - server auth
    - client auth
  privateKey:
    algorithm: RSA
    encoding: PKCS8
  dnsNames:
    - opensearch-cluster-master
    - opensearch-cluster-master.$(ENV).svc.cluster.local
  issuerRef:
    name: opensearch-cert-issuer
---
# Admin certificate for OpenSearch management tasks
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: opensearch-cluster-admin-cert
  labels:
    app: opensearch
    component: certificates
spec:
  secretName: opensearch-cluster-admin-cert-secret
  isCA: false
  commonName: opensearch-admin
  usages:
    - client auth
  privateKey:
    algorithm: RSA
    encoding: PKCS8
  issuerRef:
    name: opensearch-cert-issuer
