apiVersion: v1
kind: ConfigMap
metadata:
  name: opensearch-cluster-master-config
  labels:
    helm.sh/chart: opensearch-2.27.0
    app.kubernetes.io/name: opensearch
    app.kubernetes.io/instance: opensearch
    app.kubernetes.io/version: "2.18.0"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: opensearch-cluster-master
data:
  opensearch.yml: |
    cluster.name: opensearch-cluster
    
    # Bind to all interfaces because we don't know what IP address Docker will assign to us.
    network.host: 0.0.0.0
    
    # Setting network.host to a non-loopback address enables the annoying bootstrap checks. "Single-node" mode disables them again.
    # Implicitly done if ".singleNode" is set to "true".
    # discovery.type: single-node
    
    # Start OpenSearch Security Configuration
    # WARNING: revise all the lines below before you go into production
    plugins:
      security:
        # needed for initializing security index
        allow_default_init_securityindex: true    
    
        # Trusted node certificates
        nodes_dn:
          - 'CN=opensearch-cluster-master'

        # SSL settings for transport communication (node-to-node)
        ssl:
          transport:
            pemcert_filepath: certs/node/tls.crt
            pemkey_filepath: certs/node/tls.key
            pemtrustedcas_filepath: certs/node/ca.crt
            enforce_hostname_verification: false

          # SSL settings for HTTP client communication
          http:
            enabled: true
            pemcert_filepath: certs/node/tls.crt
            pemkey_filepath: certs/node/tls.key
            pemtrustedcas_filepath: certs/node/ca.crt

        # Admin certificate settings for securityadmin.sh
        authcz:
          admin_dn:
            - 'CN=opensearch-admin'

        # Audit log configuration
        audit.type: internal_opensearch

        # Snapshot and restore privileges
        enable_snapshot_restore_privilege: true
        check_snapshot_restore_write_privileges: true

        # REST API roles
        restapi:
          roles_enabled: ["all_access", "security_rest_api_access"]

        # System indices permissions
        system_indices:
          enabled: true
          indices:
            [
              ".opendistro-alerting-config",
              ".opendistro-alerting-alert*",
              ".opendistro-anomaly-results*",
              ".opendistro-anomaly-detector*",
              ".opendistro-anomaly-checkpoints",
              ".opendistro-anomaly-detection-state",
              ".opendistro-reports-*",
              ".opendistro-notifications-*",
              ".opendistro-notebooks",
              ".opendistro-asynchronous-search-response*",
            ]
    ######## End OpenSearch Security Configuration ########
