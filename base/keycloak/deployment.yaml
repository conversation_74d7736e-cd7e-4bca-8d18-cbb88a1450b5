kind: Deployment
apiVersion: apps/v1
metadata:
  name: keycloak
spec:
  selector:
    matchLabels:
      app: keycloak
  template:
    metadata:
      annotations:
        reloader.stakater.com/auto: "true"
      labels:
        app: keycloak
    spec:
      containers:
        - image: keycloak
          name: keycloak
          readinessProbe:
            httpGet:
              path: "/auth/realms/master"
              port: 9080
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          livenessProbe:
            httpGet:
              path: "/auth/realms/master"
              port: 9080
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          args:
            - 'start'
            - '--features=admin-fine-grained-authz,token-exchange'
            - '--spi-oauth2-token-exchange-default-enabled=false'
            - '--spi-login-protocol-openid-connect-legacy-logout-redirect-uri=true'

          envFrom:
            - secretRef:
                name: keycloak-secret
            - configMapRef:
                name: keycloak-configmap
            - configMapRef:
                name: keycloak-db-config
          resources:
            requests:
              cpu: "1"
              memory: "2Gi"
            limits:
              memory: "2Gi"
          ports:
            - containerPort: 9080
      imagePullSecrets:
        - name: ocirsecret