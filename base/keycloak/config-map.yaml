apiVersion: v1
kind: ConfigMap
metadata:
  name: keycloak-configmap
data:
  KC_PROXY: "edge"
  KC_HOSTNAME_STRICT: "false"
  KC_HTTP_ENABLED: "true"
  KC_HOSTNAME_STRICT_HTTPS: "false"
  <PERSON>_HTTP_RELATIVE_PATH: "/auth"
  PROXY_ADDRESS_FORWARDING: "true"
  HOSTNAME_STRICT: "false"
  KC_DB_URL_HOST: "$(DB_HOST)"
  KC_DB_URL_DATABASE: "$(ENV)_keycloak"
  ADMIN_USER: "service-account-integration_client"
  GATEWAY: "http://gateway/graphql"
  WORK_FLOW_URL: "http://process-order-flow:8080"
  REACT_APP_API_URL: "https://api.$(ROOT_DOMAIN)/graphql"
  CUSTOMER_REGISTRATION_FEATURE_ENABLED: "$(FEATURE_FLAG_CUSTOMER_REGISTRATION_FEATURE_ENABLED)"

  DEBUG_PORT: "*:8787"

