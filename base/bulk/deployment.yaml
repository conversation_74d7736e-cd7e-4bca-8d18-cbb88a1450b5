apiVersion: apps/v1
kind: Deployment
metadata:
  name: bulk
spec:
  selector:
    matchLabels:
      app: bulk
  template:
    metadata:
      annotations:
        reloader.stakater.com/auto: "true"
      labels:
        app: bulk
    spec:
      containers:
        - image: bulk
          name: bulk
          resources:
            requests:
              cpu: "500m"
              memory: "1.5Gi"
            limits:
              memory: "1.5Gi"
          readinessProbe:
            httpGet:
              path: /management/health/readiness
              port: 8077
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          livenessProbe:
            httpGet:
              path: /management/health/liveness
              port: 8077
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          envFrom:
            - configMapRef:
                name: db-type
            - configMapRef:
                name: bulk-configmap
            - secretRef:
                name: bulk-secret
          ports:
            - containerPort: 8077
              name: bulk
      imagePullSecrets:
        - name: ocirsecret