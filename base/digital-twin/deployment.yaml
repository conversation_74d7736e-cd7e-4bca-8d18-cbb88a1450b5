apiVersion: apps/v1
kind: Deployment
metadata:
  name: digital-twin
spec:
  selector:
    matchLabels:
      app: digital-twin
  template:
    metadata:
      annotations:
        reloader.stakater.com/auto: "true"
      labels:
        app: digital-twin
    spec:
      containers:
        - image: digital-twin
          name: digital-twin
          resources:
            requests:
              cpu: 200m
              memory: 1.5Gi
            limits:
              cpu: "1"
              memory: 1.5Gi
          readinessProbe:
            httpGet:
              path: /management/health/readiness
              port: 7030
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          livenessProbe:
            httpGet:
              path: /management/health/liveness
              port: 7030
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          envFrom:
            - configMapRef:
                name: db-type
            - configMapRef:
                name: digital-twin-configmap
            - secretRef:
                name: digital-twin-secret
          ports:
            - containerPort: 7030
              name: digital-twin
      imagePullSecrets:
        - name: ocirsecret