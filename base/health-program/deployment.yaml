apiVersion: apps/v1
kind: Deployment
metadata:
  name: health-program
spec:
# TODO: research the best approach for rolling updates, to handle liquibase migrations lock, maybe 1 by 1 is the best approach ?
#  strategy:
#    rollingUpdate:
#      maxSurge: 1
#      maxUnavailable: 1
#    type: RollingUpdate
  selector:
    matchLabels:
      app: health-program
  template:
    metadata:
      annotations:
        reloader.stakater.com/auto: "true"
      labels:
        app: health-program
    spec:
      containers:
        - image: health-program
          name: health-program
          resources:
            requests:
              cpu: 500m
              memory: 1.5Gi
            limits:
              memory: 1.5Gi
          readinessProbe:
            httpGet:
              path: /management/health/readiness
              port: 8000
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          livenessProbe:
            httpGet:
              path: /management/health/liveness
              port: 8000
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          envFrom:
            - configMapRef:
                name: db-type
            - configMapRef:
                name: health-program-configmap
            - secretRef:
                name: health-program-secret
          ports:
            - containerPort: 8000
              name: health-program
      imagePullSecrets:
        - name: oci<PERSON><PERSON><PERSON>