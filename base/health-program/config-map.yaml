apiVersion: v1
kind: ConfigMap
metadata:
  name: health-program-configmap
data:
  DB_HOST: "$(DB_HOST)"
  ENV: "$(ENV)"
  SERVER_SERVLET_CONTEXT_PATH: ""
  APOLLO_GRAPHQL_CLIENT_API_URL: "http://gateway/graphql"
  SPRING_KAFKA_BOOTSTRAP_SERVERS: "kafka-cluster-kafka-bootstrap:9092"
  SPRING_KAFKA_PRODUCER_BOOTSTRAP_SERVERS: "kafka-cluster-kafka-bootstrap:9092"
  SPRING_SECURITY_OAUTH2_CLIENT_PROVIDER_OIDC_ISSUER_URI: "https://api.$(ROOT_DOMAIN)/auth/realms/$(KEYCLOAK_REALM)"
  GOOGLE_SPACE_URL: "$(HEALTH_PROGRAM_GOOGLE_SPACE_URL)"
  workflow.api-base-url: "http://process-order-flow:8080"
  file-service.base-url: "https://api.$(ROOT_DOMAIN)/file-service"
  INTEGRATION_INTEGRATION_SERVICE_GATEWAY: "http://integration-gateway:8033/"
  spring.patient-profile-consent-access.approve-period: "15"
  spring.patient-profile-consent-access.access-period: "120"
  INTEGRATION_API_BASE_URL: "http://vendor-integration-api:7020/api/"
  SPRING_JPA_SHOW_SQL: "false"
  SPRING_JPA_PROPERTIES_HIBERNATE_FORMAT_SQL: "false"
  PAYER_INTEGRATION_API_BASE_URL: "http://payer-integration-api:7010/api/"
  OPEN_SEARCH_HOST_NAME: "$(OPEN_SEARCH_HOST_NAME)"
  OPEN_SEARCH_USERNAME: "$(OPEN_SEARCH_USERNAME)"
  OPEN_SEARCH_HOST_SCHEMA: "$(OPEN_SEARCH_HOST_SCHEMA)"
  OPEN_SEARCH_PORT: "$(OPEN_SEARCH_PORT)"
  meetora.host: "$(MEETORA_BACKEND_BASE_DOMAIN)"
  meetora.port: "$(MEETORA_BACKEND_PORT)"
  meetora.modules: "$(MEETORA_MODULES)"
  meetora.meeting-platform-chat-view-url: "https://$(MEETORA_UI_BASE_DOMAIN)/o/@organization_id/chat-view/@anonymous_chat_token?auth_token=@oauth_token&exit_url=@exit_url"
  meetora.meeting-platform-join-chat-url: "https://$(MEETORA_UI_BASE_DOMAIN)/o/@organization_id/joinchat/@anonymous_chat_token?auth_token=@oauth_token&exit_url=@exit_url"
  meetora.meeting-platform-join-call-url: "https://$(MEETORA_UI_BASE_DOMAIN)/o/@organization_id/join-call/@anonymous_chat_token?auth_token=@oauth_token&exit_url=@exit_url"
  appointment-confirm.email.provider-dashboard-link: "https://provider.$(ROOT_DOMAIN)/appointments"
  ENAYATI_PAYER_LICENSE_NUMBER: "INS-TEST2022"
#  TZ: "Asia/Dubai"
  FEATURE_FLAG_MULTIPLE_APPOINTMENT_SLOTS_FEATURE_ENABLED: "$(FEATURE_FLAG_MULTIPLE_APPOINTMENT_SLOTS_FEATURE_ENABLED)"
  FEATURE_FLAG_MEDICAL_DEVICE_FEATURE_ENABLED: "$(FEATURE_FLAG_MEDICAL_DEVICE_FEATURE_ENABLED)"
  FEATURE_FLAG_IMMEDIATE_CALL_FEATURE_ENABLED: "$(FEATURE_FLAG_IMMEDIATE_CALL_FEATURE_ENABLED)"
  FEATURE_FLAG_GUEST_APPOINTMENT_FEATURE_ENABLED: "$(FEATURE_FLAG_GUEST_APPOINTMENT_FEATURE_ENABLED)"
  FEATURE_FLAG_HOME_CARE_SERVICES_FEATURE_ENABLED: "$(FEATURE_FLAG_HOME_CARE_SERVICES_FEATURE_ENABLED)"
  GUIDED_CARE_TEAM_FILTER_ELIGIBLE_TEAM_BY_PATIENT_NETWORK: "$(GUIDED_CARE_TEAM_FILTER_ELIGIBLE_TEAM_BY_PATIENT_NETWORK)"
  FEATURE_FLAG_PROGRAM_PROFILE_FEATURE_ENABLED: "$(FEATURE_FLAG_PROGRAM_PROFILE_FEATURE_ENABLED)"
  FEATURE_FLAG_REFERRAL_FEATURE_ENABLED: "$(FEATURE_FLAG_REFERRAL_FEATURE_ENABLED)"