apiVersion: v1
kind: Service
metadata:
  annotations:
    service.beta.kubernetes.io/oci-load-balancer-shape: "flexible"
    service.beta.kubernetes.io/oci-load-balancer-shape-flex-min: "10"  # Minimum bandwidth in Mbps
    service.beta.kubernetes.io/oci-load-balancer-shape-flex-max: "100"
  labels:
    app: tolgee
  name: tolgee
spec:
  type: LoadBalancer
  ports:
    - name: http
      protocol: TCP
      port: 8080
  selector:
    app: tolgee