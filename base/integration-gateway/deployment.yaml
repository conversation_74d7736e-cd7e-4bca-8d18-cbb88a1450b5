apiVersion: apps/v1
kind: Deployment
metadata:
  name: integration-gateway
spec:
  selector:
    matchLabels:
      app: integration-gateway
  template:
    metadata:
      annotations:
        reloader.stakater.com/auto: "true"
      labels:
        app: integration-gateway
    spec:
      containers:
        - image: integration-gateway
          name: integration-gateway
          resources:
            requests:
              cpu: 300m
              memory: 1.5Gi
            limits:
              memory: 1.5Gi
          readinessProbe:
            httpGet:
              path: /management/health/readiness
              port: 8033
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          livenessProbe:
            httpGet:
              path: /management/health/liveness
              port: 8033
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          envFrom:
            - configMapRef:
                name: integration-gateway-configmap
          ports:
            - containerPort: 8033
      imagePullSecrets:
        - name: ocirsecret