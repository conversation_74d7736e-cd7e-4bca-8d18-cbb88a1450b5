apiVersion: apps/v1
kind: Deployment
metadata:
  name: integration-eclaim
spec:
  selector:
    matchLabels:
      app: integration-eclaim
  template:
    metadata:
      annotations:
        reloader.stakater.com/auto: "true"
      labels:
        app: integration-eclaim
    spec:
      containers:
        - image: integration-eclaim
          name: integration-eclaim
          resources:
            requests:
              cpu: 300m
              memory: 1.5Gi
            limits:
              memory: 1.5Gi
          readinessProbe:
            httpGet:
              path: /management/health/readiness
              port: 8087
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          livenessProbe:
            httpGet:
              path: /management/health/liveness
              port: 8087
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          envFrom:
            - configMapRef:
                name: db-type
            - configMapRef:
                name: integration-eclaim-configmap
            - secretRef:
                name: integration-eclaim-secret
          ports:
            - containerPort: 8087
      imagePullSecrets:
        - name: ocirsecret