apiVersion: v1
kind: ConfigMap
metadata:
  name: integration-eclaim-configmap
data:
  DB_HOST: "$(DB_HOST)"
  ENV: "$(ENV)"
  SPRING_KAFKA_BOOTSTRAP_SERVERS: "kafka-cluster-kafka-bootstrap:9092"
  SPRING_KAFKA_PRODUCER_BOOTSTRAP_SERVERS: "kafka-cluster-kafka-bootstrap:9092"
  SPRING_SECURITY_OAUTH2_CLIENT_PROVIDER_OIDC_ISSUER_URI: "https://api.$(ROOT_DOMAIN)/auth/realms/$(KEYCLOAK_REALM)"
  INTEGRATION_GATEWAY_URL: "http://gateway/graphql"
  INTEGRATION_WORKFLOW_URL: "http://process-order-flow:8080/workflow/v1"
  INTEGRATION_ORDER_PRESCRIPTION_URL: "https://client.$(ROOT_DOMAIN)/order-prescription"
  INTEGRATION_KEYCLOAK_CLIENT_ID: "integration_client"
  TZ: "Asia/Dubai"
  GOOGLE_SPACE_URL: "$(ECLAIM_GOOGLE_SPACE_URL)"