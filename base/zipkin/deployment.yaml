apiVersion: apps/v1
kind: Deployment
metadata:
  name: zipkin
spec:
  replicas: 1
  selector:
    matchLabels:
      app: zipkin
  template:
    metadata:
      annotations:
        reloader.stakater.com/auto: "true"
      labels:
        app: zipkin
    spec:
      containers:
        - image: zipkin
          name: zipkin
          ports:
            - containerPort: 9411
              name: zipkin
          volumeMounts:
            - name: keycloak-private-key-volume
              mountPath: app/keys/sehatuk.pem
              subPath: sehatuk.pem
          envFrom:
            - configMapRef:
                name: zipkin-configmap
            - secretRef:
                name: zipkin-secret
      imagePullSecrets:
        - name: ocirsecret
      volumes:
        - name: keycloak-private-key-volume
          configMap:
            name:
              zipkin-configmap