apiVersion: apps/v1
kind: Deployment
metadata:
  name: sehatuk-optima-ui
spec:
  replicas: 1
  selector:
    matchLabels:
      app: sehatuk-optima-ui
  template:
    metadata:
      annotations:
        reloader.stakater.com/auto: "true"
      labels:
        app: sehatuk-optima-ui
    spec:
      containers:
        - name: sehatuk-optima-ui
          image: sehatuk-optima-ui
          envFrom:
            - configMapRef:
                name: sehatuk-ui-shared-configmap
            - configMapRef:
                name: sehatuk-optima-ui-configmap
          readinessProbe:
            httpGet:
              path: /login
              port: 3000
            initialDelaySeconds: 5
            periodSeconds: 5
            failureThreshold: 3
          livenessProbe:
            tcpSocket:
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3000
      imagePullSecrets:
        - name: ocirsecret