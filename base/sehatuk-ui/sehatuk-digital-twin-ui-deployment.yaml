apiVersion: apps/v1
kind: Deployment
metadata:
  name: sehatuk-digital-twin-ui
spec:
  selector:
    matchLabels:
      app: sehatuk-digital-twin-ui
  template:
    metadata:
      annotations:
        reloader.stakater.com/auto: "true"
      labels:
        app: sehatuk-digital-twin-ui
    spec:
      containers:
        - name: sehatuk-digital-twin-ui
          image: sehatuk-digital-twin-ui
          envFrom:
            - configMapRef:
                name: sehatuk-ui-shared-configmap
            - configMapRef:
                name: sehatuk-digital-twin-ui-configmap
          readinessProbe:
            httpGet:
              path: /login
              port: 3000
            initialDelaySeconds: 5
            periodSeconds: 5
            failureThreshold: 3
          livenessProbe:
            tcpSocket:
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3000
      imagePullSecrets:
        - name: ocirsecret
