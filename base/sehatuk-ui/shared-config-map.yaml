apiVersion: v1
kind: ConfigMap
metadata:
  name: sehatuk-ui-shared-configmap
data:
  SERVER_URL: "https://api.$(ROOT_DOMAIN)"
  BASE_URL: "https://api.$(ROOT_DOMAIN)/gateway/graphql"
  SUBSCRIPTION_URL: "https://api.$(ROOT_DOMAIN)/subscription/api/subscription"
  FILE_SERVICE_API_URL: "https://api.$(ROOT_DOMAIN)/file-service/api"
  BASE_MEDIA_URL: "https://api.$(ROOT_DOMAIN)/media/"
  GOOGLE_API_KEY: "AIzaSyBXx2smTW7DzhZrD37toVlDS1OWylJFmuA"
  KEYCLOAK_BASE_URL: "https://api.$(ROOT_DOMAIN)/auth"
  KEYCLOAK_REALM: "$(KEYCLOAK_REALM)"
  KEYCLOAK_REALM_LINK: "https://api.$(ROOT_DOMAIN)/auth/realms/$(KEYCLOAK_REALM)"
  KEYCLOAK_API_URL: "https://api.$(ROOT_DOMAIN)/auth/realms/$(KEYCLOAK_REALM)/protocol/openid-connect"
  ENABLE_TOLGEE_WIZARD: "true"
  TOLGEE_API_URL: "https://tolgee.stg.iohealth.com"
  # production_api_key
  TOLGEE_API_KEY: "tgpak_gjpwcobxmnsdimtdnqzgi3dcgyzgs3lfnzwtsyltnvuhg"
  TOLGEE_PROJECT_ID: "2"