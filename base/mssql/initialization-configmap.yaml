apiVersion: v1
kind: ConfigMap
metadata:
  name: mssql-init-scripts
  namespace: "default"
  labels:
    app.kubernetes.io/name: mssql
    helm.sh/chart: mssql-1.1.3
    app.kubernetes.io/instance: mssql
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/version: "2022-CU14-GDR1-rhel-9.1"
    app.kubernetes.io/component: primary
data:
  00_create_db_owner_user.sql: |
    -- Credits for https://stackoverflow.com/a/52484134/747579
    USE [master]
    GO
    CREATE LOGIN [test_db_user] WITH PASSWORD=N'$(MSSQL_PASSWORD)'
    GO
    USE [master]
    GO
    CREATE USER [test_db_user] FOR LOGIN [test_db_user]
    GO
    USE [test]
    GO
    CREATE USER [test_db_user] FOR LOGIN [test_db_user]
    ALTER ROLE db_owner ADD MEMBER [test_db_user]
    GO
