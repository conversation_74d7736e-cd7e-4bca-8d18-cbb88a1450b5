apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: mssql
  namespace: "default"
  labels:
    app.kubernetes.io/name: mssql
    helm.sh/chart: mssql-1.1.3
    app.kubernetes.io/instance: mssql
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/version: "2022-CU14-GDR1-rhel-9.1"
    app.kubernetes.io/component: primary
spec:
  replicas: 1
  podManagementPolicy: ""
  selector:
    matchLabels: 
      app.kubernetes.io/name: mssql
      app.kubernetes.io/instance: mssql
      app.kubernetes.io/component: primary
  serviceName: mssql
  updateStrategy:
    type: RollingUpdate
  template:
    metadata:
      annotations:
        checksum/configuration: 8ffbb3b8c45321b52739b8f01d3bf5ac6f4741fb0c72d75f5f2eb76b23eb819d
      labels:
        app.kubernetes.io/name: mssql
        helm.sh/chart: mssql-1.1.3
        app.kubernetes.io/instance: mssql
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/version: "2022-CU14-GDR1-rhel-9.1"
        app.kubernetes.io/component: primary
    spec:
      serviceAccountName: mssql
      
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchLabels:
                    app.kubernetes.io/instance: mssql
                    app.kubernetes.io/name: mssql
                topologyKey: kubernetes.io/hostname
              weight: 1
          
      nodeSelector:
        kubernetes.io/os: linux
      securityContext:
        fsGroup: 1001
      initContainers:
        - name: volume-permissions
          image: registry.access.redhat.com/ubi9/ubi:9.4-947.**********
          imagePullPolicy: "IfNotPresent"
          command:
            - /bin/bash
            - -ec
            - |
              mkdir -p "/var/opt/mssql" "/var/opt/mssql/backup"
              chown "1001:1001" "/var/opt/mssql" "/var/opt/mssql/backup"
              find "/var/opt/mssql" -mindepth 1 -maxdepth 1 -not -name ".snapshot" -not -name "lost+found" | xargs -r chown -R "1001:1001"
              find "/var/opt/mssql/backup" -mindepth 1 -maxdepth 1 -not -name ".snapshot" -not -name "lost+found" | xargs -r chown -R "1001:1001"
          securityContext:
            runAsUser: 0
          volumeMounts:
            - name: data
              mountPath: /var/opt/mssql
            - name: backup
              mountPath: /var/opt/mssql/backup
      containers:
        - name: mssql
          image: registry.gitlab.com/xrow-public/helm-mssql/mssql:1.1.3
          imagePullPolicy: "IfNotPresent"
          securityContext:
            runAsNonRoot: true
            runAsUser: 1001
          env:
          # https://docs.microsoft.com/en-us/sql/linux/sql-server-linux-configure-environment-variables?view=sql-server-ver16
            - name: ACCEPT_EULA
              value: "Y"
            - name: SQLCMD_ACCEPT_EULA
              value: "YES"
            - name: MSSQL_PID
              value: Express
            - name: MSSQL_TCP_PORT
              value: "1433"
            - name: MSSQL_SA_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: mssql
                  key: mssql-root-password
            - name: MSSQL_USER
              value: "test_db_user"
            - name: MSSQL_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: mssql
                  key: mssql-password
            - name: SQLCMDPASSWORD
              valueFrom:
                secretKeyRef:
                  name: mssql
                  key: mssql-password
            - name: MSSQL_DATABASE
              value: "test"
            - name: MSSQL_DATABASE_COLLATE
              value: "Latin1_General_CI_AS"
            - name: MSSQL_COLLATION
              value: "Latin1_General_CI_AS"
            - name: MSSQL_DATA_DIR
              value: /var/opt/mssql
            - name: MSSQL_BACKUP_DIR
              value: /var/opt/mssql/backup
          ports:
            - name: mssql
              containerPort: 1433
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 5
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
            exec:
              command:
                - /bin/bash
                - -ec
                - |
                  sqlcmd -C -S localhost -U sa -P "$MSSQL_SA_PASSWORD" -Q "SELECT 1"
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 5
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
            exec:
              command:
                - /bin/bash
                - -ec
                - |
                  sqlcmd -C -S localhost -U sa -P "$MSSQL_SA_PASSWORD" -Q "USE test"
          startupProbe:
            failureThreshold: 10
            initialDelaySeconds: 30
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
            exec:
              command:
                - /bin/bash
                - -ec
                - |
                  sqlcmd -C -S localhost -U sa -P "$MSSQL_SA_PASSWORD" -Q "USE test"
#          resources:
#            limits:
#              memory: 4G
#            requests:
#              cpu: 2000m
#              memory: 2G
          volumeMounts:
            - name: data
              mountPath: /var/opt/mssql
            - name: backup
              mountPath: /var/opt/mssql/backup
            - name: init-scripts-configmap
              mountPath: /tmp/init-scripts-configmap
      volumes:
        - name: init-scripts-configmap
          configMap:
            name: mssql-init-scripts
        - name: backup
          emptyDir: {}
  volumeClaimTemplates:
    - metadata:
        name: data
        labels: 
          app.kubernetes.io/name: mssql
          app.kubernetes.io/instance: mssql
          app.kubernetes.io/component: primary
      spec:
        accessModes:
          - "ReadWriteOnce"
        resources:
          requests:
            storage: "8Gi"
