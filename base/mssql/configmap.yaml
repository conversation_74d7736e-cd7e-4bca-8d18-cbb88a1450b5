apiVersion: v1
kind: ConfigMap
metadata:
  name: mssql
  namespace: "default"
  labels:
    app.kubernetes.io/name: mssql
    helm.sh/chart: mssql-1.1.3
    app.kubernetes.io/instance: mssql
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/version: "2022-CU14-GDR1-rhel-9.1"
    app.kubernetes.io/component: primary
data:
  # https://github.com/microsoft/mssql-docker/blob/master/linux/sample-helm-chart/templates/mssqlconfig.yaml
  # More params: https://github.com/Microdust/mssqlserver-docker/blob/master/mssql.conf
  mssql.conf: |
    [EULA]
    accepteula = Y
    accepteulaml = Y

    [coredump]
    captureminiandfull = true
    coredumptype = full
    
    [hadr]
    hadrenabled = 1
    
    [language]
    lcid = 1033
    
    [filelocation]
    defaultdatadir = /var/opt/mssql
    defaultlogdir = /var/opt/mssql/log
