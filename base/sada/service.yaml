apiVersion: v1
kind: Service
metadata:
  name: sada-php-backend
spec:
  type: ClusterIP
  ports:
    - name: http
      protocol: TCP
      port: 8100
      targetPort: 80
    - name: https
      protocol: TCP
      port: 443
      targetPort: 443
  selector:
    app: sada-php-backend
---
apiVersion: v1
kind: Service
metadata:
  name: sada-ws-backend
spec:
  type: ClusterIP
  ports:
    - protocol: TCP
      port: 3000
      targetPort: 3000
  selector:
    app: sada-ws-backend
---
apiVersion: v1
kind: Service
metadata:
  name: sada-frontend
spec:
  type: ClusterIP
  ports:
    - name: http
      protocol: TCP
      port: 80
      targetPort: 80
    - name: https
      protocol: TCP
      port: 443
      targetPort: 443
  selector:
    app: sada-frontend