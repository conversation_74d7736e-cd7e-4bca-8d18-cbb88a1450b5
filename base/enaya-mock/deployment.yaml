apiVersion: apps/v1
kind: Deployment
metadata:
  name: enaya-mock
spec:
  selector:
    matchLabels:
      app: enaya-mock
  template:
    metadata:
      annotations:
        reloader.stakater.com/auto: "true"
      labels:
        app: enaya-mock
    spec:
      containers:
        - image: enaya-mock
          name: enaya-mock
          readinessProbe:
            httpGet:
              path: /management/health/readiness
              port: 1010
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          livenessProbe:
            httpGet:
              path: /management/health/liveness
              port: 1010
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
          envFrom:
            - configMapRef:
                name: enaya-mock-configmap
            - secretRef:
                name: enaya-mock-secret
          ports:
            - containerPort: 1010
      imagePullSecrets:
        - name: ocirsecret